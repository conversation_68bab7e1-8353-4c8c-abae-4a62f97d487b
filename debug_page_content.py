#!/usr/bin/env python3
"""
Debug script to test Confluence page content retrieval
"""
import requests
from requests.auth import HTTPBasicAuth

def test_page_content():
    # Configuration from your working example
    CONFLUENCE_URL = "https://sdecloud.atlassian.net"
    USERNAME = input("Enter username/email: ").strip()
    API_TOKEN = input("Enter API token: ").strip()
    PAGE_ID = "837091329"  # From the URL you provided
    
    print(f"🔍 Testing page content retrieval for page ID: {PAGE_ID}")
    print(f"   Base URL: {CONFLUENCE_URL}")
    print(f"   Username: {USERNAME}")
    print()
    
    # Test different endpoint variations
    endpoints_to_test = [
        f"/rest/api/content/{PAGE_ID}",
        f"/rest/api/content/{PAGE_ID}?expand=body.storage",
        f"/rest/api/content/{PAGE_ID}?expand=body.view",
        f"/wiki/rest/api/content/{PAGE_ID}",
        f"/wiki/rest/api/content/{PAGE_ID}?expand=body.storage",
    ]
    
    for endpoint in endpoints_to_test:
        print(f"🧪 Testing endpoint: {endpoint}")
        url = f"{CONFLUENCE_URL}{endpoint}"
        print(f"   Full URL: {url}")
        
        try:
            response = requests.get(
                url,
                auth=HTTPBasicAuth(USERNAME, API_TOKEN),
                headers={"Accept": "application/json"},
                timeout=10
            )
            
            print(f"   Status: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                print(f"   ✅ SUCCESS!")
                print(f"   Title: {data.get('title', 'N/A')}")
                print(f"   Type: {data.get('type', 'N/A')}")
                print(f"   Space: {data.get('space', {}).get('key', 'N/A')}")
                
                # Check available body formats
                body = data.get('body', {})
                print(f"   Available body formats: {list(body.keys())}")
                
                # Try to get some content
                for format_name in ['storage', 'view', 'export_view']:
                    if format_name in body:
                        content = body[format_name].get('value', '')
                        print(f"   {format_name} content length: {len(content)} chars")
                        if content:
                            # Show first 200 chars
                            preview = content[:200].replace('\n', ' ')
                            print(f"   {format_name} preview: {preview}...")
                
                print(f"   🎉 This endpoint works! Use: {endpoint}")
                return endpoint
                
            elif response.status_code == 401:
                print(f"   ❌ Authentication failed")
            elif response.status_code == 403:
                print(f"   ❌ Access forbidden")
            elif response.status_code == 404:
                print(f"   ❌ Not found")
            else:
                print(f"   ❌ Error: {response.text[:200]}")
                
        except Exception as e:
            print(f"   ❌ Exception: {str(e)}")
        
        print()
    
    print("❌ No working endpoint found")
    return None

if __name__ == "__main__":
    test_page_content()
