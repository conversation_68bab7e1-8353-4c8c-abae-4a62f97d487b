"""
Input validation utilities for Confluence URL Extractor
"""
import re
from urllib.parse import urlparse
from typing import Op<PERSON>

from exceptions import ValidationError


def validate_confluence_url(url: str) -> str:
    """
    Validate Confluence URL format
    
    Args:
        url: URL to validate
        
    Returns:
        Normalized URL
        
    Raises:
        ValidationError: If URL is invalid
    """
    if not url:
        raise ValidationError("Confluence URL is required")
    
    # Add protocol if missing
    if not url.startswith(('http://', 'https://')):
        url = f"https://{url}"
    
    try:
        parsed = urlparse(url)
        if not parsed.netloc or parsed.netloc.startswith(':'):
            raise ValidationError("Invalid URL format")

        # Check for malformed URLs
        if '://' in url and url.count('://') > 1:
            raise ValidationError("Invalid URL format - multiple protocols")

        # Check for common Confluence patterns
        if not any(pattern in parsed.netloc.lower() for pattern in ['atlassian.net', 'confluence']):
            # Warning but not error - could be self-hosted
            pass

        return url.rstrip('/')

    except Exception as e:
        raise ValidationError(f"Invalid URL format: {str(e)}")


def validate_email(email: str) -> str:
    """
    Validate email format
    
    Args:
        email: Email to validate
        
    Returns:
        Validated email
        
    Raises:
        ValidationError: If email is invalid
    """
    if not email:
        raise ValidationError("Email is required")
    
    email_pattern = re.compile(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$')
    if not email_pattern.match(email):
        raise ValidationError("Invalid email format")
    
    return email.lower()


def validate_api_token(token: str) -> str:
    """
    Validate API token format
    
    Args:
        token: API token to validate
        
    Returns:
        Validated token
        
    Raises:
        ValidationError: If token is invalid
    """
    if not token:
        raise ValidationError("API token is required")
    
    if len(token) < 10:
        raise ValidationError("API token appears to be too short")
    
    # Remove any whitespace
    token = token.strip()
    
    return token


def validate_page_id(page_id: str) -> str:
    """
    Validate Confluence page ID
    
    Args:
        page_id: Page ID to validate
        
    Returns:
        Validated page ID
        
    Raises:
        ValidationError: If page ID is invalid
    """
    if not page_id:
        raise ValidationError("Page ID is required")
    
    # Remove any whitespace
    page_id = page_id.strip()
    
    # Check if it's numeric (most common format)
    if page_id.isdigit():
        return page_id
    
    # Check if it's alphanumeric (some Confluence instances use this)
    if re.match(r'^[a-zA-Z0-9]+$', page_id):
        return page_id
    
    raise ValidationError("Invalid page ID format. Should be numeric or alphanumeric.")


def validate_output_directory(directory: str) -> str:
    """
    Validate output directory path
    
    Args:
        directory: Directory path to validate
        
    Returns:
        Validated directory path
        
    Raises:
        ValidationError: If directory path is invalid
    """
    if not directory:
        raise ValidationError("Output directory is required")
    
    # Remove any whitespace
    directory = directory.strip()
    
    # Check for invalid characters (basic check)
    invalid_chars = ['<', '>', ':', '"', '|', '?', '*']
    if any(char in directory for char in invalid_chars):
        raise ValidationError("Output directory contains invalid characters")
    
    return directory


def validate_timeout(timeout: int) -> int:
    """
    Validate timeout value
    
    Args:
        timeout: Timeout in seconds
        
    Returns:
        Validated timeout
        
    Raises:
        ValidationError: If timeout is invalid
    """
    if timeout <= 0:
        raise ValidationError("Timeout must be greater than 0")
    
    if timeout > 300:  # 5 minutes max
        raise ValidationError("Timeout cannot exceed 300 seconds")
    
    return timeout


def validate_max_retries(max_retries: int) -> int:
    """
    Validate max retries value
    
    Args:
        max_retries: Maximum number of retries
        
    Returns:
        Validated max retries
        
    Raises:
        ValidationError: If max retries is invalid
    """
    if max_retries < 0:
        raise ValidationError("Max retries cannot be negative")
    
    if max_retries > 10:
        raise ValidationError("Max retries cannot exceed 10")
    
    return max_retries


def validate_rate_limit_delay(delay: float) -> float:
    """
    Validate rate limit delay value
    
    Args:
        delay: Delay in seconds
        
    Returns:
        Validated delay
        
    Raises:
        ValidationError: If delay is invalid
    """
    if delay < 0:
        raise ValidationError("Rate limit delay cannot be negative")
    
    if delay > 10:
        raise ValidationError("Rate limit delay cannot exceed 10 seconds")
    
    return delay
