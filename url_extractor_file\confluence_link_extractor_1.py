# usage: script.py [-h] [-o OUTPUT] path
# 
# Extract links from Confluence HTML exports
# 
# positional arguments:
#   path                  Path to HTML file or folder containing HTML files
# 
# optional arguments:
#   -h, --help            show this help message and exit
#   -o OUTPUT, --output OUTPUT
#                         Output directory (default: ./output)

# python confluence_link_extractor.py Pg1_Affin.html -o my_output
# python confluence_link_extractor.py ./input_files --output reports

import os
from bs4 import BeautifulSoup
import urllib.parse
import json
from datetime import datetime
import csv
from collections import defaultdict

def process_html_file(file_path):
    """Extract all links from a single HTML file"""
    if not os.path.exists(file_path):
        print(f"Error: File not found at {file_path}")
        return None, None
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            soup = BeautifulSoup(f.read(), 'html.parser')
    except Exception as e:
        print(f"Error reading HTML file: {str(e)}")
        return None, None
    
    links = []
    base_url = None
    base_tag = soup.find('base', href=True)
    if base_tag:
        base_url = base_tag['href']
    
    for a_tag in soup.find_all('a', href=True):
        href = a_tag['href'].strip()
        text = a_tag.get_text(strip=True) or href
        
        if href.startswith(('#', 'javascript:', 'mailto:')):
            continue
        
        if base_url and not urllib.parse.urlparse(href).netloc:
            href = urllib.parse.urljoin(base_url, href)
        
        link_class = ' '.join(a_tag.get('class', [])) if a_tag.get('class') else None
        
        links.append({
            'text': text,
            'url': href,
            'domain': urllib.parse.urlparse(href).netloc,
            # 'element_id': a_tag.get('id'),
            # 'class': link_class,
            'title': a_tag.get('title')
        })
    
    return links, os.path.splitext(os.path.basename(file_path))[0]

def categorize_links(links):
    """Categorize links by domain"""
    domain_dict = defaultdict(list)
    for link in links:
        domain_dict[link['domain']].append(link)
    return dict(domain_dict)

def save_raw_links(links, base_filename, output_dir='output'):
    """Save raw links to JSON and CSV"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    os.makedirs(output_dir, exist_ok=True)
    
    # JSON output
    json_file = os.path.join(output_dir, f"{base_filename}_Raw_{timestamp}.json")
    with open(json_file, 'w', encoding='utf-8') as f:
        json.dump(links, f, indent=2, ensure_ascii=False)
    
    # CSV output
    csv_file = os.path.join(output_dir, f"{base_filename}_Raw_{timestamp}.csv")
    with open(csv_file, 'w', encoding='utf-8', newline='') as f:
        writer = csv.writer(f)
        writer.writerow(['Text', 'URL', 'Domain', 'Class', 'Title', 'Element ID'])
        for link in links:
            writer.writerow([
                link['text'],
                link['url'],
                link['domain'],
                # link['class'] or '',
                link['title'] or ''
                # link['element_id'] or ''
            ])
    
    return json_file, csv_file

def save_categorized_links(categorized_links, base_filename, output_dir='output'):
    """Save categorized links to JSON and CSV"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    os.makedirs(output_dir, exist_ok=True)
    
    # JSON output
    json_file = os.path.join(output_dir, f"{base_filename}_Ordered_{timestamp}.json")
    with open(json_file, 'w', encoding='utf-8') as f:
        json.dump(categorized_links, f, indent=2, ensure_ascii=False)
    
    # CSV output
    csv_file = os.path.join(output_dir, f"{base_filename}_Ordered_{timestamp}.csv")
    with open(csv_file, 'w', encoding='utf-8', newline='') as f:
        writer = csv.writer(f)
        writer.writerow(['Domain', 'Count', 'Example URLs'])
        for domain, links in categorized_links.items():
            example_urls = '\n'.join([link['url'] for link in links[:3]])
            writer.writerow([
                domain,
                len(links),
                example_urls
            ])
    
    return json_file, csv_file

def process_single_file(file_path, output_dir='output'):
    """Process a single HTML file"""
    print(f"\nProcessing file: {file_path}")
    links, base_filename = process_html_file(file_path)
    if not links:
        return
    
    # Save raw links
    raw_json, raw_csv = save_raw_links(links, base_filename, output_dir)
    print(f"Saved raw links to:\n- {raw_json}\n- {raw_csv}")
    
    # Categorize and save
    categorized = categorize_links(links)
    ordered_json, ordered_csv = save_categorized_links(categorized, base_filename, output_dir)
    print(f"Saved categorized links to:\n- {ordered_json}\n- {ordered_csv}")
    
    # Print summary
    print(f"\nExtracted {len(links)} links from {base_filename}")
    print(f"Found {len(categorized)} unique domains")
    print("Top domains:")
    for domain in sorted(categorized.keys(), key=lambda x: len(categorized[x]), reverse=True)[:5]:
        print(f"- {domain}: {len(categorized[domain])} links")

def process_folder(folder_path, output_dir='output'):
    """Process all HTML files in a folder"""
    if not os.path.isdir(folder_path):
        print(f"Error: {folder_path} is not a valid directory")
        return
    
    html_files = [f for f in os.listdir(folder_path) if f.lower().endswith('.html')]
    if not html_files:
        print(f"No HTML files found in {folder_path}")
        return
    
    print(f"\nFound {len(html_files)} HTML files in {folder_path}")
    for file_name in html_files:
        file_path = os.path.join(folder_path, file_name)
        process_single_file(file_path, output_dir)

def main():
    import argparse
    
    parser = argparse.ArgumentParser(description='Extract links from Confluence HTML exports')
    parser.add_argument('path', help='Path to HTML file or folder containing HTML files')
    parser.add_argument('-o', '--output', default='output', help='Output directory (default: ./output)')
    
    args = parser.parse_args()
    
    if os.path.isfile(args.path):
        process_single_file(args.path, args.output)
    elif os.path.isdir(args.path):
        process_folder(args.path, args.output)
    else:
        print(f"Error: Path {args.path} is neither a file nor a directory")

if __name__ == "__main__":
    main()