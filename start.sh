#!/bin/bash
# Confluence URL Extractor - Unix/Linux Start Script
# ==================================================

# Color codes
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
BOLD='\033[1m'
NC='\033[0m' # No Color

echo
echo -e "${BLUE}================================================================${NC}"
echo -e "${BOLD} 🔗 CONFLUENCE URL EXTRACTOR - UNIX/LINUX LAUNCHER${NC}"
echo -e "${BLUE} Production-Ready URL Extraction Tool${NC}"
echo -e "${BLUE}================================================================${NC}"
echo

# Check if Python is installed
if ! command -v python3 &> /dev/null && ! command -v python &> /dev/null; then
    echo -e "${RED}❌ Python is not installed or not in PATH${NC}"
    echo -e "${RED}Please install Python 3.8+ using your package manager${NC}"
    echo
    exit 1
fi

# Determine Python command
if command -v python3 &> /dev/null; then
    PYTHON_CMD="python3"
else
    PYTHON_CMD="python"
fi

echo -e "${GREEN}✅ Python found: $($PYTHON_CMD --version)${NC}"
echo

# Check if start.py exists
if [ ! -f "start.py" ]; then
    echo -e "${RED}❌ start.py not found in current directory${NC}"
    echo -e "${RED}Please make sure you're in the correct project directory${NC}"
    echo
    exit 1
fi

echo -e "${BLUE}🚀 Starting Confluence URL Extractor...${NC}"
echo

# Make scripts executable
chmod +x start.py 2>/dev/null
chmod +x main.py 2>/dev/null
chmod +x stop.py 2>/dev/null

# Run the Python start script
$PYTHON_CMD start.py

echo
echo -e "${BLUE}📝 To extract URLs, use:${NC}"
echo -e "${GREEN}   $PYTHON_CMD main.py extract --page-id YOUR_PAGE_ID${NC}"
echo
echo -e "${BLUE}🛑 To stop and cleanup, use:${NC}"
echo -e "${GREEN}   $PYTHON_CMD stop.py${NC}"
echo
echo -e "${BLUE}📚 For help, use:${NC}"
echo -e "${GREEN}   $PYTHON_CMD main.py --help${NC}"
echo

# Ask if user wants to continue with setup
read -p "Would you like to run the setup now? (y/n): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo -e "${GREEN}🔧 Starting setup...${NC}"
    $PYTHON_CMD main.py setup
fi

echo -e "${GREEN}🎉 Ready to extract URLs from Confluence pages!${NC}"
