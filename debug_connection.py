#!/usr/bin/env python3
"""
Debug script to test Confluence connection
"""
import sys
from config import ConfluenceConfig
from confluence_client import ConfluenceClient
from logger import setup_logger

def main():
    # Setup debug logging
    logger = setup_logger(level="DEBUG")
    
    print("🔍 Confluence Connection Debug Tool")
    print("=" * 50)
    
    # Get credentials
    confluence_url = input("Confluence URL: ").strip()
    username = input("Username: ").strip()
    api_token = input("API Token: ").strip()
    
    print(f"\n📋 Configuration:")
    print(f"   URL: {confluence_url}")
    print(f"   Username: {username}")
    print(f"   Token: {'*' * (len(api_token) - 4) + api_token[-4:] if len(api_token) > 4 else '****'}")
    
    try:
        # Create config
        config = ConfluenceConfig(
            confluence_url=confluence_url,
            username=username,
            api_token=api_token
        )
        
        print(f"\n🔧 Processed URL: {config.confluence_url}")
        
        # Test connection
        print("\n🔗 Testing connection...")
        with ConfluenceClient(config) as client:
            print(f"   Base URL: {client.base_url}")
            
            # Test basic connectivity
            import requests
            print("\n🌐 Testing basic HTTP connectivity...")
            try:
                response = requests.get(config.confluence_url, timeout=10)
                print(f"   HTTP Status: {response.status_code}")
                print(f"   Response headers: {dict(response.headers)}")
            except Exception as e:
                print(f"   HTTP Error: {str(e)}")
            
            # Test API endpoints
            print("\n🔍 Testing API endpoints...")
            
            # Test 1: User current endpoint
            try:
                print("   Testing /rest/api/user/current...")
                response = client._make_request('GET', '/rest/api/user/current')
                print(f"   ✅ Success: {response.status_code}")
                user_data = response.json()
                print(f"   User: {user_data.get('displayName', 'Unknown')}")
            except Exception as e:
                print(f"   ❌ Failed: {str(e)}")
                
                # Test 2: Space endpoint
                try:
                    print("   Testing /rest/api/space...")
                    response = client._make_request('GET', '/rest/api/space')
                    print(f"   ✅ Success: {response.status_code}")
                except Exception as e2:
                    print(f"   ❌ Failed: {str(e2)}")
                    
                    # Test 3: Content endpoint
                    try:
                        print("   Testing /rest/api/content...")
                        response = client._make_request('GET', '/rest/api/content')
                        print(f"   ✅ Success: {response.status_code}")
                    except Exception as e3:
                        print(f"   ❌ Failed: {str(e3)}")
            
            # Final test using the official method
            print("\n🧪 Testing official connection method...")
            if client.test_connection():
                print("   ✅ Connection successful!")
            else:
                print("   ❌ Connection failed!")
                
    except Exception as e:
        print(f"\n❌ Error: {str(e)}")
        import traceback
        print(f"\n📋 Full traceback:")
        traceback.print_exc()

if __name__ == "__main__":
    main()
