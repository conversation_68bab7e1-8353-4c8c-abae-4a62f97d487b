#!/usr/bin/env python3
"""
Analyze the HTML content to find embedded data or alternative content sources
"""
import re
import json
from config import ConfluenceConfig
from confluence_client import ConfluenceClient

def analyze_page_content():
    """Analyze the HTML content from the Confluence page"""
    
    config = ConfluenceConfig(
        confluence_url="https://sdecloud.atlassian.net",
        username="imitiyaz",
        api_token=input("Enter API token: ").strip(),
        page_id="837091329",
        space_key="affin",
        page_title="Environment Info - Azure"
    )
    
    print("🔍 Analyzing Confluence page content...")
    
    try:
        with ConfluenceClient(config) as client:
            # Get the HTML content
            html_content = client.get_page_html_content_direct(
                config.page_id, config.space_key, config.page_title
            )
            
            print(f"📊 HTML Content: {len(html_content):,} characters")
            
            # Save full HTML for inspection
            with open('full_page_content.html', 'w', encoding='utf-8') as f:
                f.write(html_content)
            print("💾 Saved full HTML to: full_page_content.html")
            
            # Look for embedded JSON data
            print("\n🔍 Searching for embedded data...")
            
            # Pattern 1: Look for JSON data in script tags
            json_patterns = [
                r'window\.__INITIAL_STATE__\s*=\s*({.+?});',
                r'window\.__CONFLUENCE_STATE__\s*=\s*({.+?});',
                r'AJS\.Meta\.set\(["\']([^"\']+)["\']\s*,\s*({.+?})\)',
                r'data-options=["\']({.+?})["\']',
                r'<script[^>]*>.*?var\s+\w+\s*=\s*({.+?});.*?</script>',
            ]
            
            for i, pattern in enumerate(json_patterns, 1):
                matches = re.findall(pattern, html_content, re.DOTALL | re.IGNORECASE)
                print(f"   Pattern {i}: {len(matches)} matches")
                
                if matches:
                    for j, match in enumerate(matches[:3], 1):  # Show first 3 matches
                        try:
                            if isinstance(match, tuple):
                                json_data = json.loads(match[-1])  # Last element is usually the JSON
                            else:
                                json_data = json.loads(match)
                            print(f"      Match {j}: Valid JSON with {len(json_data)} keys")
                            if isinstance(json_data, dict):
                                print(f"         Keys: {list(json_data.keys())[:10]}")
                        except:
                            print(f"      Match {j}: Invalid JSON")
            
            # Pattern 2: Look for content in specific div/section tags
            print("\n🔍 Searching for content containers...")
            content_patterns = [
                r'<div[^>]*class="[^"]*content[^"]*"[^>]*>(.*?)</div>',
                r'<div[^>]*id="[^"]*content[^"]*"[^>]*>(.*?)</div>',
                r'<main[^>]*>(.*?)</main>',
                r'<article[^>]*>(.*?)</article>',
                r'<section[^>]*class="[^"]*page[^"]*"[^>]*>(.*?)</section>',
            ]
            
            for i, pattern in enumerate(content_patterns, 1):
                matches = re.findall(pattern, html_content, re.DOTALL | re.IGNORECASE)
                print(f"   Content pattern {i}: {len(matches)} matches")
                
                if matches:
                    for j, match in enumerate(matches[:2], 1):  # Show first 2 matches
                        # Count links in this content
                        links = re.findall(r'<a[^>]*href=["\']([^"\']*)["\'][^>]*>', match)
                        print(f"      Match {j}: {len(match)} chars, {len(links)} links")
                        if links:
                            print(f"         Sample links: {links[:3]}")
            
            # Pattern 3: Look for any href attributes in the entire content
            print("\n🔍 Searching for ALL href attributes...")
            all_hrefs = re.findall(r'href=["\']([^"\']*)["\']', html_content)
            print(f"   Total href attributes found: {len(all_hrefs)}")
            
            if all_hrefs:
                # Categorize hrefs
                http_links = [h for h in all_hrefs if h.startswith('http')]
                relative_links = [h for h in all_hrefs if h.startswith('/')]
                other_links = [h for h in all_hrefs if not h.startswith(('http', '/'))]
                
                print(f"   HTTP links: {len(http_links)}")
                print(f"   Relative links: {len(relative_links)}")
                print(f"   Other links: {len(other_links)}")
                
                # Show samples
                if http_links:
                    print(f"   Sample HTTP links: {http_links[:5]}")
                if relative_links:
                    print(f"   Sample relative links: {relative_links[:5]}")
            
            # Pattern 4: Look for specific Confluence content markers
            print("\n🔍 Searching for Confluence-specific content...")
            confluence_markers = [
                r'data-page-id=["\']([^"\']*)["\']',
                r'data-space-key=["\']([^"\']*)["\']',
                r'confluence-content',
                r'wiki-content',
                r'page-content',
            ]
            
            for marker in confluence_markers:
                matches = re.findall(marker, html_content, re.IGNORECASE)
                print(f"   {marker}: {len(matches)} matches")
                if matches:
                    print(f"      Values: {matches[:5]}")
            
            print("\n✅ Analysis complete!")
            print("📁 Check full_page_content.html for manual inspection")
            
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    analyze_page_content()
