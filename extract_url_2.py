import requests
from requests.auth import HTTPBasicAuth
import json
import urllib.parse
from bs4 import BeautifulSoup

# Confluence API configuration
CONFLUENCE_URL = "https://sdecloud.atlassian.net"
USERNAME = "<EMAIL>"  # Replace with your email
API_TOKEN = "ATATT3xFfGF0ctOI2eFgHkxeBInkwRcVI1oF-XliI8lJsZcy6cnq_JgmCNLhJIho3J5KQ3c3-G6nFq013TGTP7KTc2Bq_HUK7C1v9_Sf6XPA5Gh1FUq_8IH6O-vb_J93LuulNhu4JFklh7acNIopAtiq0p5KGxLi3A8Tcq1yA82ofj0ZH-j118g=5AF05E80"         # Replace with your API token
PAGE_ID = "*********"                # From your URL
SPACE_KEY = "DEVOPS"                 # From your URL
PAGE_TITLE = "Environment+Info+-+<PERSON>yun+-+Affin"                  # From your URL

def get_page_content():
    # Try multiple API approaches to get the content
    
    # Approach 1: By page ID (what you originally tried)
    endpoint = f"{CONFLUENCE_URL}/rest/api/content/{PAGE_ID}?expand=body.view"
    print(f"endpoint: {endpoint} ")
    response = requests.get(
        endpoint,
        auth=HTTPBasicAuth(USERNAME, API_TOKEN),
        headers={"Accept": "application/json"}
    )
    
    if response.status_code == 200:
        return response.json()
    
    # Approach 2: By space key and title if ID approach fails
    print(f"Approach 1 failed (status {response.status_code}), trying by space and title...")
    title_encoded = urllib.parse.quote(PAGE_TITLE)
    endpoint = f"{CONFLUENCE_URL}/rest/api/content?title={title_encoded}&spaceKey={SPACE_KEY}&expand=body.view"
    
    response = requests.get(
        endpoint,
        auth=HTTPBasicAuth(USERNAME, API_TOKEN),
        headers={"Accept": "application/json"}
    )
    
    if response.status_code == 200:
        results = response.json().get('results', [])
        if results:
            return results[0]  # Return first matching page
    
    # If both approaches fail
    print(f"Failed to get page content. Status: {response.status_code}, Response: {response.text}")
    return None

def extract_links_from_page(page_data):
    if not page_data:
        return []
    
    # Try different content formats
    html_content = None
    for format in ['view', 'storage', 'export_view', 'atlas_doc_format']:
        content = page_data.get('body', {}).get(format, {}).get('value')
        if content:
            html_content = content
            break
    
    if not html_content:
        print("Could not find page content in any format")
        return []
    
    soup = BeautifulSoup(html_content, 'html.parser')
    links = []
    
    for link in soup.find_all('a', href=True):
        href = link['href']
        text = link.get_text(strip=True)
        
        # Skip anchor links and javascript
        if href.startswith(('#', 'javascript:')):
            continue
            
        # Convert relative URLs
        if href.startswith('/'):
            href = urllib.parse.urljoin(CONFLUENCE_URL, href)
        
        links.append({
            'text': text,
            'url': href,
            'element': str(link)  # Optional: store the original HTML element
        })
    
    return links

if __name__ == "__main__":
    print(f"Extracting links from: {CONFLUENCE_URL}/wiki/spaces/{SPACE_KEY}/pages/{PAGE_ID}/{PAGE_TITLE}")
    
    page_data = get_page_content()
    links = extract_links_from_page(page_data)
    
    if links:
        print(f"\nFound {len(links)} links:")
        for i, link in enumerate(links, 1):
            print(f"{i}. [{link['text']}]({link['url']})")
        
        # Save to JSON file
        with open('confluence_links.json', 'w') as f:
            json.dump(links, f, indent=2)
        print("\nLinks saved to 'confluence_links.json'")
        
        # Save to Markdown (optional)
        with open('confluence_links.md', 'w') as f:
            f.write(f"# Links from {PAGE_TITLE}\n\n")
            for link in links:
                f.write(f"- [{link['text']}]({link['url']})\n")
        print("Links also saved to 'confluence_links.md'")
    else:
        print("No links found or couldn't access the page content")