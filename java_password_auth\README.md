# Java Password Authentication Extractor

This implementation uses **username/password authentication** instead of API tokens to establish a proper browser-like session with Confluence.

## 🎯 **Purpose**

This approach solves the authentication issue by:

1. **Establishing a real session** - Like a browser login
2. **Getting session cookies** - Automatic cookie management
3. **Accessing restricted content** - Pages that require interactive login
4. **Extracting all URLs** - From the actual page content

## 🔐 **How It Works**

### **Authentication Flow:**
1. **Get login page** - Retrieve Confluence login form
2. **Extract tokens** - Get CSRF/ATL tokens from the form
3. **Submit credentials** - POST username/password with tokens
4. **Establish session** - Get authenticated session cookies
5. **Access target page** - Use session to get actual content

### **Content Extraction:**
1. **Direct page access** - Try the main page URL
2. **View mode variants** - Try different view parameters
3. **API with session** - Use REST API with session cookies
4. **URL extraction** - Parse HTML and extract all links

## 🚀 **Quick Start**

### Windows
```bash
cd java_password_auth
build.bat
```

### Linux/Mac
```bash
cd java_password_auth
chmod +x build.sh
./build.sh
```

### Manual Execution
```bash
cd java_password_auth
javac PasswordAuthExtractor.java
java PasswordAuthExtractor
```

## 📋 **Usage**

When you run the extractor, you'll be prompted for:

```
Enter Confluence username: your-username
Enter Confluence password: your-password
```

**Note**: Use the same credentials you use to log into Confluence in your browser.

## 🔧 **What It Tests**

### **Authentication Steps:**
1. **Login Page Access** - Gets the Confluence login form
2. **Token Extraction** - Extracts CSRF/ATL tokens
3. **Credential Submission** - Posts login form with credentials
4. **Session Validation** - Checks for successful authentication

### **Content Access Methods:**
1. **Direct Page Access** - `https://sdecloud.atlassian.net/wiki/spaces/affin/pages/837091329/Environment+Info+-+Azure`
2. **View Modes** - `?view=content`, `?decorator=printable`, etc.
3. **API Endpoints** - REST API calls with session cookies
4. **Content Extraction** - HTML parsing and URL extraction

## 📁 **Output Files**

The extractor generates several files:

- **`direct_page_content.html`** - Content from direct page access
- **`view_mode_content.html`** - Content from view mode access
- **`api_session_content.json`** - JSON response from API
- **`*_links.txt`** - Extracted URLs with metadata
- **`*_debug.html`** - Debug content for troubleshooting

## ✅ **Expected Results**

### **Successful Authentication:**
```
🔐 Starting Authentication Flow
📄 Step 1: Getting login page...
✅ Login page retrieved: 45,231 chars
🔑 Extracted tokens:
   ATL Token: abc123def4...
🔓 Step 2: Performing login...
✅ Login successful!
```

### **Successful Content Extraction:**
```
📄 Step 3: Accessing target page...
🌐 Trying direct page access...
   Content size: 1,284,291 chars
   ✅ SUCCESS! Got actual page content
   💾 Saved content to: direct_page_content.html
   🔗 Extracted 45 unique URLs to: direct_page_content_links.txt
   🎉 SUCCESS! Found substantial content with many links!
```

## 🔍 **Troubleshooting**

### **Authentication Issues**
- **Wrong credentials**: Verify username/password are correct
- **Account locked**: Check if account is locked due to failed attempts
- **2FA enabled**: This method may not work with two-factor authentication
- **SSO required**: Some organizations require SSO login

### **Content Access Issues**
- **Still getting redirects**: Authentication may have failed
- **Empty content**: Page may not exist or be accessible
- **Few URLs found**: Page content may be dynamically loaded

### **Network Issues**
- **Connection timeout**: Check internet connection
- **SSL errors**: May need to configure SSL certificates
- **Proxy issues**: Configure proxy settings if needed

## 🆚 **Comparison with API Token Approach**

| Aspect | API Token | Username/Password |
|--------|-----------|-------------------|
| **Authentication** | Basic Auth | Session-based |
| **Session cookies** | ❌ No | ✅ Yes |
| **Restricted pages** | ❌ Limited | ✅ Full access |
| **Interactive login** | ❌ No | ✅ Yes |
| **2FA support** | ✅ Yes | ❌ Limited |
| **Security** | ✅ Token-based | ⚠️ Password-based |

## 🔒 **Security Considerations**

### **Password Handling:**
- Passwords are entered interactively (not stored in code)
- Passwords are not logged or saved to files
- Session cookies are managed automatically

### **Best Practices:**
- Use this approach only for testing/development
- Consider using API tokens for production
- Implement proper credential management
- Use secure connections (HTTPS)

## 🎯 **Why This Should Work**

### **Session-Based Authentication:**
- **Mimics browser behavior** - Establishes real session
- **Gets all cookies** - Including authentication and permission cookies
- **Handles redirects** - Follows authentication flow properly
- **Access restricted content** - Pages requiring interactive login

### **Comprehensive Content Access:**
- **Multiple access methods** - Direct, view modes, API
- **Fallback strategies** - If one method fails, try others
- **Complete URL extraction** - Parse all links from actual content
- **Debug information** - Save intermediate results for analysis

## 🚀 **Next Steps**

1. **Run the extractor** with your Confluence credentials
2. **Check output files** for extracted URLs
3. **Verify results** against what you see in browser
4. **Integrate solution** into your main application

**This approach should successfully extract all URLs from the Environment Info - Azure page!** 🎉
