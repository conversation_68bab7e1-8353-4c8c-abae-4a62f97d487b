import java.io.*;
import java.net.*;
import java.nio.charset.StandardCharsets;
import java.util.*;

/**
 * Test version of password authentication to demonstrate the flow
 * This version shows what the authentication process looks like
 */
public class TestPasswordAuth {
    
    private static final String CONFLUENCE_URL = "https://sdecloud.atlassian.net";
    private CookieManager cookieManager;
    
    public static void main(String[] args) {
        TestPasswordAuth tester = new TestPasswordAuth();
        
        System.out.println("🔐 Password Authentication Flow Test");
        System.out.println("=" + "=".repeat(50));
        System.out.println("Target: " + CONFLUENCE_URL);
        System.out.println();
        
        tester.testAuthenticationFlow();
    }
    
    public TestPasswordAuth() {
        // Setup cookie management for session handling
        this.cookieManager = new CookieManager();
        CookieHandler.setDefault(cookieManager);
    }
    
    /**
     * Test the authentication flow without actual credentials
     */
    private void testAuthenticationFlow() {
        try {
            System.out.println("🔍 Testing Authentication Flow Components");
            System.out.println("-".repeat(40));
            
            // Step 1: Test login page access
            testLoginPageAccess();
            
            // Step 2: Test form analysis
            testFormAnalysis();
            
            // Step 3: Test session management
            testSessionManagement();
            
            // Step 4: Show what happens with real credentials
            showRealAuthenticationFlow();
            
        } catch (Exception e) {
            System.out.println("❌ Test error: " + e.getMessage());
        }
    }
    
    /**
     * Test accessing the login page
     */
    private void testLoginPageAccess() {
        System.out.println("📄 Step 1: Testing Login Page Access");
        
        try {
            String loginUrl = CONFLUENCE_URL + "/login.action";
            HttpURLConnection conn = createConnection(loginUrl);
            
            int responseCode = conn.getResponseCode();
            System.out.println("   Login page response: " + responseCode);
            
            if (responseCode == 200) {
                String content = readResponse(conn);
                System.out.println("   ✅ Login page accessible: " + content.length() + " chars");
                
                // Check for login form elements
                boolean hasUsernameField = content.contains("os_username") || content.contains("username");
                boolean hasPasswordField = content.contains("os_password") || content.contains("password");
                boolean hasLoginForm = content.contains("form") && content.contains("login");
                
                System.out.println("   Form analysis:");
                System.out.println("     Username field: " + (hasUsernameField ? "✅ Found" : "❌ Missing"));
                System.out.println("     Password field: " + (hasPasswordField ? "✅ Found" : "❌ Missing"));
                System.out.println("     Login form: " + (hasLoginForm ? "✅ Found" : "❌ Missing"));
                
                if (hasUsernameField && hasPasswordField && hasLoginForm) {
                    System.out.println("   🎉 Login form is properly accessible!");
                }
                
            } else if (responseCode == 302 || responseCode == 301) {
                String location = conn.getHeaderField("Location");
                System.out.println("   🔄 Redirected to: " + location);
                
                if (location != null && location.contains("id.atlassian.com")) {
                    System.out.println("   ℹ️  Uses Atlassian ID for authentication");
                }
            } else {
                System.out.println("   ⚠️  Unexpected response code: " + responseCode);
            }
            
        } catch (Exception e) {
            System.out.println("   ❌ Login page test failed: " + e.getMessage());
        }
        
        System.out.println();
    }
    
    /**
     * Test form analysis capabilities
     */
    private void testFormAnalysis() {
        System.out.println("🔍 Step 2: Testing Form Analysis");
        
        // Simulate typical Confluence login form HTML
        String sampleForm = """
            <form action="/dologin.action" method="post">
                <input type="hidden" name="atl_token" value="abc123def456">
                <input type="hidden" name="atlassian-token" value="xyz789uvw012">
                <input type="text" name="os_username" placeholder="Username">
                <input type="password" name="os_password" placeholder="Password">
                <input type="submit" value="Log in">
            </form>
            """;
        
        System.out.println("   Testing token extraction...");
        
        String atlToken = extractToken(sampleForm, "atl_token");
        String xsrfToken = extractToken(sampleForm, "atlassian-token");
        
        System.out.println("   Token extraction results:");
        System.out.println("     ATL Token: " + (atlToken != null ? "✅ " + atlToken : "❌ Not found"));
        System.out.println("     XSRF Token: " + (xsrfToken != null ? "✅ " + xsrfToken : "❌ Not found"));
        
        if (atlToken != null && xsrfToken != null) {
            System.out.println("   🎉 Token extraction working correctly!");
        }
        
        System.out.println();
    }
    
    /**
     * Test session management
     */
    private void testSessionManagement() {
        System.out.println("🍪 Step 3: Testing Session Management");
        
        System.out.println("   Cookie manager status: " + (cookieManager != null ? "✅ Active" : "❌ Inactive"));
        
        // Test cookie storage
        try {
            CookieStore store = cookieManager.getCookieStore();
            System.out.println("   Cookie store: " + (store != null ? "✅ Available" : "❌ Unavailable"));
            System.out.println("   Current cookies: " + store.getCookies().size());
            
            // Simulate adding a session cookie
            HttpCookie testCookie = new HttpCookie("JSESSIONID", "test123");
            testCookie.setDomain("sdecloud.atlassian.net");
            testCookie.setPath("/");
            
            URI uri = new URI(CONFLUENCE_URL);
            store.add(uri, testCookie);
            
            System.out.println("   Test cookie added: " + store.getCookies().size() + " total cookies");
            System.out.println("   🎉 Session management working correctly!");
            
        } catch (Exception e) {
            System.out.println("   ⚠️  Session management test failed: " + e.getMessage());
        }
        
        System.out.println();
    }
    
    /**
     * Show what the real authentication flow would look like
     */
    private void showRealAuthenticationFlow() {
        System.out.println("🚀 Step 4: Real Authentication Flow");
        System.out.println("-".repeat(40));
        
        System.out.println("With real credentials, the flow would be:");
        System.out.println();
        
        System.out.println("1. 📄 Get login page:");
        System.out.println("   GET " + CONFLUENCE_URL + "/login.action");
        System.out.println("   Extract: atl_token, atlassian-token");
        System.out.println();
        
        System.out.println("2. 🔐 Submit credentials:");
        System.out.println("   POST " + CONFLUENCE_URL + "/dologin.action");
        System.out.println("   Data: os_username=YOUR_USERNAME");
        System.out.println("         os_password=YOUR_PASSWORD");
        System.out.println("         atl_token=EXTRACTED_TOKEN");
        System.out.println("         atlassian-token=EXTRACTED_TOKEN");
        System.out.println();
        
        System.out.println("3. 🍪 Get session cookies:");
        System.out.println("   Response: 302 Redirect to dashboard");
        System.out.println("   Cookies: JSESSIONID, confluence.browse.space.cookie, etc.");
        System.out.println();
        
        System.out.println("4. 📄 Access target page:");
        System.out.println("   GET https://sdecloud.atlassian.net/wiki/spaces/affin/pages/837091329");
        System.out.println("   Headers: Cookie: JSESSIONID=...; confluence.browse.space.cookie=...");
        System.out.println("   Result: ✅ Actual page content with all URLs!");
        System.out.println();
        
        System.out.println("🎯 Expected Results:");
        System.out.println("   ✅ Status 200 (not redirect to login)");
        System.out.println("   ✅ Large content size (1MB+ instead of 88KB)");
        System.out.println("   ✅ Many URLs extracted (50+ instead of 2)");
        System.out.println("   ✅ Actual page content (not JavaScript shell)");
        System.out.println();
        
        System.out.println("📋 To run with real credentials:");
        System.out.println("   java PasswordAuthExtractor");
        System.out.println("   Enter your Confluence username and password when prompted");
        System.out.println();
        
        System.out.println("🔒 Security Notes:");
        System.out.println("   • Credentials are entered interactively (not stored in code)");
        System.out.println("   • Session cookies are managed automatically");
        System.out.println("   • Use the same credentials you use in your browser");
        System.out.println("   • This approach mimics browser authentication exactly");
    }
    
    /**
     * Extract token from HTML using regex
     */
    private String extractToken(String html, String tokenName) {
        java.util.regex.Pattern pattern = java.util.regex.Pattern.compile(
            "name=[\"']" + tokenName + "[\"']\\s+value=[\"']([^\"']+)[\"']|" +
            "value=[\"']([^\"']+)[\"']\\s+name=[\"']" + tokenName + "[\"']",
            java.util.regex.Pattern.CASE_INSENSITIVE
        );
        
        java.util.regex.Matcher matcher = pattern.matcher(html);
        if (matcher.find()) {
            return matcher.group(1) != null ? matcher.group(1) : matcher.group(2);
        }
        
        return null;
    }
    
    /**
     * Create HTTP connection
     */
    private HttpURLConnection createConnection(String url) throws IOException {
        URL urlObj = new URL(url);
        HttpURLConnection conn = (HttpURLConnection) urlObj.openConnection();
        
        // Browser-like headers
        conn.setRequestProperty("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36");
        conn.setRequestProperty("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8");
        conn.setRequestProperty("Accept-Language", "en-US,en;q=0.5");
        conn.setRequestProperty("Connection", "keep-alive");
        
        conn.setConnectTimeout(15000);
        conn.setReadTimeout(30000);
        
        return conn;
    }
    
    /**
     * Read response
     */
    private String readResponse(HttpURLConnection conn) throws IOException {
        InputStream inputStream = conn.getInputStream();
        
        String encoding = conn.getContentEncoding();
        if ("gzip".equals(encoding)) {
            inputStream = new java.util.zip.GZIPInputStream(inputStream);
        }
        
        try (BufferedReader reader = new BufferedReader(
                new InputStreamReader(inputStream, StandardCharsets.UTF_8))) {
            StringBuilder response = new StringBuilder();
            String line;
            while ((line = reader.readLine()) != null) {
                response.append(line).append("\n");
            }
            return response.toString();
        }
    }
}
