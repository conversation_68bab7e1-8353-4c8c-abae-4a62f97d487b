import java.io.*;
import java.net.*;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Confluence URL extractor using username/password authentication
 * This approach establishes a proper session like a browser would
 */
public class PasswordAuthExtractor {
    
    private static final String CONFLUENCE_URL = "https://sdecloud.atlassian.net";
    private static final String TARGET_PAGE = "https://sdecloud.atlassian.net/wiki/spaces/affin/pages/837091329/Environment+Info+-+Azure";
    
    private CookieManager cookieManager;
    private String username;
    private String password;
    
    public static void main(String[] args) {
        PasswordAuthExtractor extractor = new PasswordAuthExtractor();
        
        System.out.println("🔐 Password Authentication Extractor");
        System.out.println("=" + "=".repeat(50));
        System.out.println("Target: " + TARGET_PAGE);
        System.out.println();
        
        // Get credentials from user
        extractor.getCredentials();
        
        // Perform authentication and extraction
        extractor.performAuthentication();
    }
    
    public PasswordAuthExtractor() {
        // Setup cookie management for session handling
        this.cookieManager = new CookieManager();
        CookieHandler.setDefault(cookieManager);
    }
    
    /**
     * Get username and password from user
     */
    private void getCredentials() {
        Scanner scanner = new Scanner(System.in);
        
        System.out.print("Enter Confluence username: ");
        this.username = scanner.nextLine().trim();
        
        System.out.print("Enter Confluence password: ");
        this.password = scanner.nextLine().trim();
        
        System.out.println();
        System.out.println("✅ Credentials captured");
        System.out.println("Username: " + username);
        System.out.println("Password: " + "*".repeat(password.length()));
        System.out.println();
    }
    
    /**
     * Perform complete authentication flow
     */
    private void performAuthentication() {
        try {
            System.out.println("🔐 Starting Authentication Flow");
            System.out.println("-".repeat(40));
            
            // Step 1: Get login page and extract tokens
            System.out.println("📄 Step 1: Getting login page...");
            String loginPageUrl = CONFLUENCE_URL + "/login.action";
            String loginPageContent = getPage(loginPageUrl);
            
            if (loginPageContent == null) {
                System.out.println("❌ Failed to get login page");
                return;
            }
            
            System.out.println("✅ Login page retrieved: " + loginPageContent.length() + " chars");
            
            // Extract authentication tokens
            String atlToken = extractToken(loginPageContent, "atl_token");
            String xsrfToken = extractToken(loginPageContent, "atlassian-token");
            
            System.out.println("🔑 Extracted tokens:");
            if (atlToken != null) {
                System.out.println("   ATL Token: " + atlToken.substring(0, Math.min(10, atlToken.length())) + "...");
            }
            if (xsrfToken != null) {
                System.out.println("   XSRF Token: " + xsrfToken.substring(0, Math.min(10, xsrfToken.length())) + "...");
            }
            
            // Step 2: Perform login
            System.out.println("\n🔓 Step 2: Performing login...");
            boolean loginSuccess = performLogin(atlToken, xsrfToken);
            
            if (!loginSuccess) {
                System.out.println("❌ Login failed");
                return;
            }
            
            System.out.println("✅ Login successful!");
            
            // Step 3: Access target page
            System.out.println("\n📄 Step 3: Accessing target page...");
            accessTargetPage();
            
        } catch (Exception e) {
            System.out.println("❌ Authentication error: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * Get a page with current session
     */
    private String getPage(String url) {
        try {
            HttpURLConnection conn = createConnection(url);
            conn.setRequestMethod("GET");
            
            int responseCode = conn.getResponseCode();
            System.out.println("   Response: " + responseCode + " for " + url);
            
            if (responseCode == 200) {
                return readResponse(conn);
            } else {
                System.out.println("   Error response: " + readErrorResponse(conn));
                return null;
            }
            
        } catch (Exception e) {
            System.out.println("   Exception getting page: " + e.getMessage());
            return null;
        }
    }
    
    /**
     * Extract authentication token from HTML
     */
    private String extractToken(String html, String tokenName) {
        // Look for hidden input with token
        Pattern pattern = Pattern.compile(
            "name=[\"']" + tokenName + "[\"']\\s+value=[\"']([^\"']+)[\"']|" +
            "value=[\"']([^\"']+)[\"']\\s+name=[\"']" + tokenName + "[\"']",
            Pattern.CASE_INSENSITIVE
        );
        
        Matcher matcher = pattern.matcher(html);
        if (matcher.find()) {
            return matcher.group(1) != null ? matcher.group(1) : matcher.group(2);
        }
        
        // Alternative: look for meta tag
        Pattern metaPattern = Pattern.compile(
            "name=[\"']" + tokenName + "[\"']\\s+content=[\"']([^\"']+)[\"']|" +
            "content=[\"']([^\"']+)[\"']\\s+name=[\"']" + tokenName + "[\"']",
            Pattern.CASE_INSENSITIVE
        );
        
        Matcher metaMatcher = metaPattern.matcher(html);
        if (metaMatcher.find()) {
            return metaMatcher.group(1) != null ? metaMatcher.group(1) : metaMatcher.group(2);
        }
        
        return null;
    }
    
    /**
     * Perform login with credentials and tokens
     */
    private boolean performLogin(String atlToken, String xsrfToken) {
        try {
            String loginUrl = CONFLUENCE_URL + "/dologin.action";
            HttpURLConnection conn = createConnection(loginUrl);
            conn.setRequestMethod("POST");
            conn.setDoOutput(true);
            
            // Set form headers
            conn.setRequestProperty("Content-Type", "application/x-www-form-urlencoded");
            conn.setRequestProperty("Referer", CONFLUENCE_URL + "/login.action");
            
            // Prepare form data
            StringBuilder formData = new StringBuilder();
            formData.append("os_username=").append(URLEncoder.encode(username, "UTF-8"));
            formData.append("&os_password=").append(URLEncoder.encode(password, "UTF-8"));
            
            if (atlToken != null) {
                formData.append("&atl_token=").append(URLEncoder.encode(atlToken, "UTF-8"));
            }
            if (xsrfToken != null) {
                formData.append("&atlassian-token=").append(URLEncoder.encode(xsrfToken, "UTF-8"));
            }
            
            formData.append("&login=Log+in");
            formData.append("&index.action=");
            
            // Send login request
            try (OutputStream os = conn.getOutputStream()) {
                os.write(formData.toString().getBytes(StandardCharsets.UTF_8));
            }
            
            int responseCode = conn.getResponseCode();
            System.out.println("   Login response: " + responseCode);
            
            // Check for successful login (usually redirect)
            if (responseCode == 302 || responseCode == 301) {
                String location = conn.getHeaderField("Location");
                System.out.println("   Redirect to: " + location);
                
                // Check if redirected to dashboard (success) or back to login (failure)
                if (location != null && !location.contains("login")) {
                    return true;
                }
            } else if (responseCode == 200) {
                String response = readResponse(conn);
                // Check if response contains success indicators
                if (!response.contains("incorrect") && !response.contains("invalid") && 
                    !response.contains("error") && response.contains("dashboard")) {
                    return true;
                }
            }
            
            return false;
            
        } catch (Exception e) {
            System.out.println("   Login exception: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * Access target page with authenticated session
     */
    private void accessTargetPage() {
        try {
            // Try multiple approaches to get the content
            tryDirectPageAccess();
            tryContentViewModes();
            tryApiEndpointsWithSession();
            
        } catch (Exception e) {
            System.out.println("❌ Target page access error: " + e.getMessage());
        }
    }
    
    /**
     * Try direct page access
     */
    private void tryDirectPageAccess() {
        System.out.println("🌐 Trying direct page access...");
        
        String content = getPage(TARGET_PAGE);
        if (content != null) {
            System.out.println("   Content size: " + content.length() + " chars");
            
            if (isActualContent(content)) {
                System.out.println("   ✅ SUCCESS! Got actual page content");
                extractAndSaveUrls(content, "direct_page_content.html");
            } else {
                System.out.println("   ⚠️  Still getting shell/redirect page");
                saveDebugContent(content, "direct_page_debug.html");
            }
        }
    }
    
    /**
     * Try different content view modes
     */
    private void tryContentViewModes() {
        System.out.println("👁️ Trying content view modes...");
        
        String[] viewModes = {
            TARGET_PAGE + "?view=content",
            TARGET_PAGE + "?mode=view", 
            TARGET_PAGE + "?decorator=printable",
            TARGET_PAGE + "?theme=documentation",
            TARGET_PAGE + "?print=content"
        };
        
        for (String url : viewModes) {
            System.out.println("   Testing: " + url.substring(url.indexOf('?')));
            String content = getPage(url);
            
            if (content != null && isActualContent(content)) {
                System.out.println("   ✅ SUCCESS with view mode!");
                extractAndSaveUrls(content, "view_mode_content.html");
                return;
            }
        }
    }
    
    /**
     * Try API endpoints with session cookies
     */
    private void tryApiEndpointsWithSession() {
        System.out.println("🔧 Trying API endpoints with session...");
        
        String[] apiEndpoints = {
            "/wiki/rest/api/content/837091329?expand=body.storage",
            "/wiki/rest/api/content/837091329?expand=body.view",
            "/wiki/rest/api/content/837091329?expand=body.export_view",
            "/wiki/rest/api/content/search?cql=id=837091329&expand=body.storage"
        };
        
        for (String endpoint : apiEndpoints) {
            System.out.println("   Testing API: " + endpoint.substring(endpoint.lastIndexOf('/') + 1));
            
            try {
                HttpURLConnection conn = createConnection(CONFLUENCE_URL + endpoint);
                conn.setRequestProperty("Accept", "application/json");
                
                int responseCode = conn.getResponseCode();
                if (responseCode == 200) {
                    String content = readResponse(conn);
                    System.out.println("   API Success: " + content.length() + " chars");
                    
                    if (content.contains("\"body\"") || content.contains("\"storage\"")) {
                        System.out.println("   ✅ SUCCESS! Found body content in API");
                        extractContentFromJson(content, "api_session_content.json");
                        return;
                    }
                }
                
            } catch (Exception e) {
                System.out.println("   API error: " + e.getMessage());
            }
        }
    }
    
    /**
     * Check if content is actual page content
     */
    private boolean isActualContent(String content) {
        return content.contains("Environment Info") || 
               content.contains("Azure") ||
               (content.contains("<a") && content.contains("href") && 
                !content.contains("id.atlassian.com") && 
                !content.contains("meta http-equiv=\"Refresh\"") &&
                content.length() > 10000); // Substantial content
    }
    
    /**
     * Extract and save URLs from HTML content
     */
    private void extractAndSaveUrls(String content, String filename) {
        try {
            // Save full content
            try (FileWriter writer = new FileWriter(filename, StandardCharsets.UTF_8)) {
                writer.write(content);
            }
            System.out.println("   💾 Saved content to: " + filename);
            
            // Extract URLs
            Pattern linkPattern = Pattern.compile("<a[^>]*href=[\"']([^\"']*)[\"'][^>]*>(.*?)</a>", 
                Pattern.CASE_INSENSITIVE | Pattern.DOTALL);
            Matcher matcher = linkPattern.matcher(content);
            
            Set<String> urls = new HashSet<>();
            StringBuilder linksList = new StringBuilder();
            linksList.append("URLs extracted from: ").append(filename).append("\n");
            linksList.append("=" + "=".repeat(60)).append("\n\n");
            
            while (matcher.find()) {
                String href = matcher.group(1);
                String text = matcher.group(2).replaceAll("<[^>]+>", "").trim();
                
                if (!href.startsWith("#") && !href.startsWith("javascript:") && !urls.contains(href)) {
                    urls.add(href);
                    
                    if (text.length() > 100) {
                        text = text.substring(0, 100) + "...";
                    }
                    
                    linksList.append(String.format("%3d. %s\n", urls.size(), href));
                    linksList.append(String.format("     Text: %s\n\n", text));
                }
            }
            
            // Save URLs
            String linksFile = filename.replace(".html", "_links.txt");
            try (FileWriter writer = new FileWriter(linksFile, StandardCharsets.UTF_8)) {
                writer.write(linksList.toString());
            }
            
            System.out.println("   🔗 Extracted " + urls.size() + " unique URLs to: " + linksFile);
            
            if (urls.size() > 10) {
                System.out.println("   🎉 SUCCESS! Found substantial content with many links!");
            }
            
        } catch (Exception e) {
            System.out.println("   ⚠️  URL extraction error: " + e.getMessage());
        }
    }
    
    /**
     * Extract content from JSON API response
     */
    private void extractContentFromJson(String jsonContent, String filename) {
        try {
            // Save JSON response
            try (FileWriter writer = new FileWriter(filename, StandardCharsets.UTF_8)) {
                writer.write(jsonContent);
            }
            System.out.println("   💾 Saved JSON to: " + filename);
            
            // Extract HTML from JSON
            Pattern bodyPattern = Pattern.compile("\"value\"\\s*:\\s*\"(.*?)\"", Pattern.DOTALL);
            Matcher matcher = bodyPattern.matcher(jsonContent);
            
            if (matcher.find()) {
                String htmlContent = matcher.group(1);
                // Unescape JSON string
                htmlContent = htmlContent.replace("\\\"", "\"")
                                       .replace("\\n", "\n")
                                       .replace("\\r", "\r")
                                       .replace("\\t", "\t")
                                       .replace("\\\\", "\\");
                
                System.out.println("   📄 Extracted HTML: " + htmlContent.length() + " chars");
                
                // Save HTML and extract URLs
                String htmlFile = filename.replace(".json", "_body.html");
                extractAndSaveUrls(htmlContent, htmlFile);
            }
            
        } catch (Exception e) {
            System.out.println("   ⚠️  JSON extraction error: " + e.getMessage());
        }
    }
    
    /**
     * Save debug content
     */
    private void saveDebugContent(String content, String filename) {
        try {
            String sample = content.length() > 5000 ? content.substring(0, 5000) : content;
            try (FileWriter writer = new FileWriter(filename, StandardCharsets.UTF_8)) {
                writer.write("Debug content sample:\n");
                writer.write("=" + "=".repeat(50) + "\n\n");
                writer.write(sample);
            }
            System.out.println("   💾 Saved debug content to: " + filename);
        } catch (Exception e) {
            System.out.println("   ⚠️  Debug save error: " + e.getMessage());
        }
    }
    
    /**
     * Create HTTP connection with session cookies
     */
    private HttpURLConnection createConnection(String url) throws IOException {
        URL urlObj = new URL(url);
        HttpURLConnection conn = (HttpURLConnection) urlObj.openConnection();
        
        // Browser-like headers
        conn.setRequestProperty("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36");
        conn.setRequestProperty("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8");
        conn.setRequestProperty("Accept-Language", "en-US,en;q=0.5");
        conn.setRequestProperty("Accept-Encoding", "gzip, deflate, br");
        conn.setRequestProperty("Connection", "keep-alive");
        conn.setRequestProperty("Upgrade-Insecure-Requests", "1");
        
        // Confluence-specific headers
        conn.setRequestProperty("X-Atlassian-Token", "no-check");
        
        conn.setConnectTimeout(15000);
        conn.setReadTimeout(30000);
        
        return conn;
    }
    
    /**
     * Read response handling compression
     */
    private String readResponse(HttpURLConnection conn) throws IOException {
        InputStream inputStream = conn.getInputStream();
        
        String encoding = conn.getContentEncoding();
        if ("gzip".equals(encoding)) {
            inputStream = new java.util.zip.GZIPInputStream(inputStream);
        }
        
        try (BufferedReader reader = new BufferedReader(
                new InputStreamReader(inputStream, StandardCharsets.UTF_8))) {
            StringBuilder response = new StringBuilder();
            String line;
            while ((line = reader.readLine()) != null) {
                response.append(line).append("\n");
            }
            return response.toString();
        }
    }
    
    /**
     * Read error response
     */
    private String readErrorResponse(HttpURLConnection conn) {
        try (BufferedReader reader = new BufferedReader(
                new InputStreamReader(conn.getErrorStream(), StandardCharsets.UTF_8))) {
            StringBuilder response = new StringBuilder();
            String line;
            while ((line = reader.readLine()) != null) {
                response.append(line).append("\n");
            }
            return response.toString();
        } catch (Exception e) {
            return "Could not read error response: " + e.getMessage();
        }
    }
}
