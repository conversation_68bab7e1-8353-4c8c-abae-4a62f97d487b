import java.io.*;
import java.net.*;
import java.nio.charset.StandardCharsets;
import java.util.Base64;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Simple page extractor that focuses on getting uncompressed content
 */
public class SimplePageExtractor {
    
    private static final String CONFLUENCE_URL = "https://sdecloud.atlassian.net";
    private static final String USERNAME = "imitiyaz";
    private static final String API_TOKEN = "ATATT3xFfGF0hpe-8j9m0miV0UUVy6JaMzsgbXht2AVRI7mPn9sekwyqo9v8yPpx9Ee3kyj8LWWiC9usd0GBSa3Wb0etrIvmqnvNf5oqRiP379Jf1me5DhggNQ_763UlHA0pGnnE6zXIHVOupY_sytIeX756FPaHoGA_pfmzRbHhi7ZPcswMvTak=6D360167";
    private static final String PAGE_URL = "https://sdecloud.atlassian.net/wiki/spaces/affin/pages/837091329/Environment+Info+-+Azure";
    
    public static void main(String[] args) {
        SimplePageExtractor extractor = new SimplePageExtractor();
        
        System.out.println("🎯 Simple Page Extractor - Getting Uncompressed Content");
        System.out.println("=" + "=".repeat(60));
        System.out.println("Target URL: " + PAGE_URL);
        System.out.println();
        
        // Try different approaches to get readable content
        extractor.tryUncompressedRequest();
        extractor.tryMobileVersion();
        extractor.tryPrintVersion();
        extractor.tryExportVersion();
        extractor.tryRawApiAccess();
    }
    
    /**
     * Try to get uncompressed content by explicitly rejecting compression
     */
    public void tryUncompressedRequest() {
        System.out.println("📄 Strategy 1: Uncompressed Request");
        System.out.println("-".repeat(40));
        
        try {
            HttpURLConnection conn = createUncompressedConnection(PAGE_URL);
            
            int status = conn.getResponseCode();
            System.out.println("   Status: " + status);
            
            if (status == 200) {
                String content = readResponseAsText(conn);
                System.out.println("   Content size: " + content.length() + " chars");
                
                // Check if content is readable
                if (isReadableContent(content)) {
                    System.out.println("   ✅ SUCCESS! Got readable content");
                    analyzeAndSaveContent(content, "uncompressed_content.html");
                } else {
                    System.out.println("   ⚠️  Content still appears compressed/binary");
                    // Save first 1000 chars for inspection
                    saveContentSample(content, "uncompressed_sample.txt");
                }
            }
            
        } catch (Exception e) {
            System.out.println("   ❌ Error: " + e.getMessage());
        }
        
        System.out.println();
    }
    
    /**
     * Try mobile version which might be simpler
     */
    public void tryMobileVersion() {
        System.out.println("📱 Strategy 2: Mobile Version");
        System.out.println("-".repeat(40));
        
        String[] mobileUrls = {
            CONFLUENCE_URL + "/wiki/spaces/affin/pages/837091329?mobile=true",
            CONFLUENCE_URL + "/wiki/spaces/affin/pages/837091329?view=mobile",
            CONFLUENCE_URL + "/wiki/spaces/affin/pages/837091329?theme=mobile"
        };
        
        for (String url : mobileUrls) {
            tryUrl(url, "mobile");
        }
        
        System.out.println();
    }
    
    /**
     * Try print version which is usually simpler
     */
    public void tryPrintVersion() {
        System.out.println("🖨️ Strategy 3: Print Version");
        System.out.println("-".repeat(40));
        
        String[] printUrls = {
            CONFLUENCE_URL + "/wiki/spaces/affin/pages/837091329?print=true",
            CONFLUENCE_URL + "/wiki/spaces/affin/pages/837091329?view=print",
            CONFLUENCE_URL + "/wiki/spaces/affin/pages/837091329?mode=print"
        };
        
        for (String url : printUrls) {
            tryUrl(url, "print");
        }
        
        System.out.println();
    }
    
    /**
     * Try export versions
     */
    public void tryExportVersion() {
        System.out.println("📤 Strategy 4: Export Versions");
        System.out.println("-".repeat(40));
        
        String[] exportUrls = {
            CONFLUENCE_URL + "/wiki/spaces/affin/pages/837091329?export=true",
            CONFLUENCE_URL + "/wiki/exportword?pageId=837091329&format=html",
            CONFLUENCE_URL + "/wiki/pages/viewpage.action?pageId=837091329&export=true"
        };
        
        for (String url : exportUrls) {
            tryUrl(url, "export");
        }
        
        System.out.println();
    }
    
    /**
     * Try raw API access with different parameters
     */
    public void tryRawApiAccess() {
        System.out.println("🔧 Strategy 5: Raw API Access");
        System.out.println("-".repeat(40));
        
        String[] apiUrls = {
            CONFLUENCE_URL + "/wiki/rest/api/content/837091329?expand=body.storage,body.view",
            CONFLUENCE_URL + "/wiki/rest/api/content/837091329?expand=body.export_view",
            CONFLUENCE_URL + "/wiki/api/v2/pages/837091329?body-format=storage",
            CONFLUENCE_URL + "/wiki/api/v2/pages/837091329?body-format=view"
        };
        
        for (String url : apiUrls) {
            tryApiUrl(url);
        }
        
        System.out.println();
    }
    
    /**
     * Create connection that explicitly rejects compression
     */
    private HttpURLConnection createUncompressedConnection(String url) throws IOException {
        URL urlObj = new URL(url);
        HttpURLConnection conn = (HttpURLConnection) urlObj.openConnection();
        
        // Basic auth
        String auth = USERNAME + ":" + API_TOKEN;
        String encodedAuth = Base64.getEncoder().encodeToString(auth.getBytes(StandardCharsets.UTF_8));
        conn.setRequestProperty("Authorization", "Basic " + encodedAuth);
        
        // Headers that request uncompressed content
        conn.setRequestProperty("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36");
        conn.setRequestProperty("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8");
        conn.setRequestProperty("Accept-Language", "en-US,en;q=0.5");
        conn.setRequestProperty("Accept-Encoding", "identity"); // Request uncompressed content
        conn.setRequestProperty("Connection", "keep-alive");
        conn.setRequestProperty("Cache-Control", "no-cache");
        
        conn.setConnectTimeout(15000);
        conn.setReadTimeout(30000);
        
        return conn;
    }
    
    /**
     * Try a specific URL
     */
    private void tryUrl(String url, String type) {
        try {
            System.out.println("   Trying " + type + ": " + url.substring(url.lastIndexOf('/') + 1));
            
            HttpURLConnection conn = createUncompressedConnection(url);
            int status = conn.getResponseCode();
            
            if (status == 200) {
                String content = readResponseAsText(conn);
                System.out.printf("   Status: %d, Size: %d chars", status, content.length());
                
                if (isReadableContent(content)) {
                    System.out.println(" ✅ READABLE!");
                    analyzeAndSaveContent(content, type + "_content.html");
                } else {
                    System.out.println(" - Binary/compressed");
                }
            } else {
                System.out.printf("   Status: %d - Failed%n", status);
            }
            
        } catch (Exception e) {
            System.out.println("   Error: " + e.getMessage());
        }
    }
    
    /**
     * Try API URL
     */
    private void tryApiUrl(String url) {
        try {
            System.out.println("   API: " + url.substring(url.lastIndexOf("rest/") + 5));
            
            HttpURLConnection conn = createUncompressedConnection(url);
            conn.setRequestProperty("Accept", "application/json");
            
            int status = conn.getResponseCode();
            
            if (status == 200) {
                String content = readResponseAsText(conn);
                System.out.printf("   Status: %d, Size: %d chars", status, content.length());
                
                if (content.contains("\"body\"") || content.contains("\"storage\"")) {
                    System.out.println(" ✅ HAS BODY!");
                    extractBodyFromJson(content, "api_response.json");
                } else {
                    System.out.println(" - No body content");
                }
            } else {
                System.out.printf("   Status: %d - Failed%n", status);
            }
            
        } catch (Exception e) {
            System.out.println("   Error: " + e.getMessage());
        }
    }
    
    /**
     * Read response as text, handling encoding properly
     */
    private String readResponseAsText(HttpURLConnection conn) throws IOException {
        InputStream inputStream = conn.getInputStream();
        
        // Check content encoding
        String encoding = conn.getContentEncoding();
        if (encoding != null && encoding.equals("gzip")) {
            System.out.println("   ⚠️  Server sent gzip despite request for identity encoding");
            inputStream = new java.util.zip.GZIPInputStream(inputStream);
        }
        
        // Read with proper charset
        String charset = getCharsetFromContentType(conn.getContentType());
        try (BufferedReader reader = new BufferedReader(
                new InputStreamReader(inputStream, charset))) {
            StringBuilder response = new StringBuilder();
            String line;
            while ((line = reader.readLine()) != null) {
                response.append(line).append("\n");
            }
            return response.toString();
        }
    }
    
    /**
     * Extract charset from content type
     */
    private String getCharsetFromContentType(String contentType) {
        if (contentType != null && contentType.contains("charset=")) {
            String[] parts = contentType.split("charset=");
            if (parts.length > 1) {
                return parts[1].split(";")[0].trim();
            }
        }
        return "UTF-8";
    }
    
    /**
     * Check if content is readable (not binary)
     */
    private boolean isReadableContent(String content) {
        if (content == null || content.length() < 100) return false;
        
        // Check for binary characters
        int binaryCount = 0;
        int totalChars = Math.min(content.length(), 1000); // Check first 1000 chars
        
        for (int i = 0; i < totalChars; i++) {
            char c = content.charAt(i);
            if (c < 32 && c != '\n' && c != '\r' && c != '\t') {
                binaryCount++;
            }
        }
        
        // If more than 5% binary characters, consider it binary
        return (binaryCount * 100.0 / totalChars) < 5.0;
    }
    
    /**
     * Analyze and save readable content
     */
    private void analyzeAndSaveContent(String content, String filename) {
        try {
            // Save content
            try (FileWriter writer = new FileWriter(filename, StandardCharsets.UTF_8)) {
                writer.write(content);
            }
            System.out.println("   💾 Saved to: " + filename);
            
            // Count links
            int linkCount = countLinks(content);
            System.out.println("   🔗 Links found: " + linkCount);
            
            // Extract and save links
            if (linkCount > 0) {
                extractLinks(content, filename);
            }
            
            // Look for page title
            if (content.contains("Environment Info") || content.contains("Azure")) {
                System.out.println("   📄 Contains target page title!");
            }
            
        } catch (Exception e) {
            System.out.println("   ⚠️  Save error: " + e.getMessage());
        }
    }
    
    /**
     * Save content sample for inspection
     */
    private void saveContentSample(String content, String filename) {
        try {
            String sample = content.length() > 2000 ? content.substring(0, 2000) : content;
            try (FileWriter writer = new FileWriter(filename, StandardCharsets.UTF_8)) {
                writer.write("Content sample (first 2000 chars):\n");
                writer.write("=" + "=".repeat(50) + "\n\n");
                writer.write(sample);
            }
            System.out.println("   💾 Saved sample to: " + filename);
        } catch (Exception e) {
            System.out.println("   ⚠️  Sample save error: " + e.getMessage());
        }
    }
    
    /**
     * Count links in content
     */
    private int countLinks(String content) {
        Pattern linkPattern = Pattern.compile("<a[^>]*href=[\"']([^\"']*)[\"'][^>]*>", Pattern.CASE_INSENSITIVE);
        Matcher matcher = linkPattern.matcher(content);
        int count = 0;
        while (matcher.find()) {
            count++;
        }
        return count;
    }
    
    /**
     * Extract links from content
     */
    private void extractLinks(String content, String sourceFile) {
        try {
            Pattern linkPattern = Pattern.compile("<a[^>]*href=[\"']([^\"']*)[\"'][^>]*>(.*?)</a>", 
                Pattern.CASE_INSENSITIVE | Pattern.DOTALL);
            Matcher matcher = linkPattern.matcher(content);
            
            StringBuilder linksList = new StringBuilder();
            linksList.append("Links extracted from: ").append(sourceFile).append("\n");
            linksList.append("=" + "=".repeat(60)).append("\n\n");
            
            int count = 0;
            while (matcher.find() && count < 100) { // Limit to 100 links
                String href = matcher.group(1);
                String text = matcher.group(2).replaceAll("<[^>]+>", "").trim();
                
                if (text.length() > 100) {
                    text = text.substring(0, 100) + "...";
                }
                
                count++;
                linksList.append(String.format("%3d. %s\n", count, href));
                linksList.append(String.format("     Text: %s\n\n", text));
            }
            
            String linksFile = sourceFile.replace(".html", "_links.txt");
            try (FileWriter writer = new FileWriter(linksFile, StandardCharsets.UTF_8)) {
                writer.write(linksList.toString());
            }
            
            System.out.println("   📋 Extracted " + count + " links to: " + linksFile);
            
        } catch (Exception e) {
            System.out.println("   ⚠️  Link extraction error: " + e.getMessage());
        }
    }
    
    /**
     * Extract body content from JSON response
     */
    private void extractBodyFromJson(String jsonContent, String filename) {
        try {
            // Save JSON response
            try (FileWriter writer = new FileWriter(filename, StandardCharsets.UTF_8)) {
                writer.write(jsonContent);
            }
            System.out.println("   💾 Saved JSON to: " + filename);
            
            // Look for body content in JSON
            Pattern bodyPattern = Pattern.compile("\"value\"\\s*:\\s*\"(.*?)\"", Pattern.DOTALL);
            Matcher matcher = bodyPattern.matcher(jsonContent);
            
            if (matcher.find()) {
                String htmlContent = matcher.group(1);
                // Unescape JSON string
                htmlContent = htmlContent.replace("\\\"", "\"")
                                       .replace("\\n", "\n")
                                       .replace("\\r", "\r")
                                       .replace("\\t", "\t")
                                       .replace("\\\\", "\\");
                
                System.out.println("   📄 Found HTML content: " + htmlContent.length() + " chars");
                
                // Save HTML content
                String htmlFile = filename.replace(".json", "_body.html");
                try (FileWriter writer = new FileWriter(htmlFile, StandardCharsets.UTF_8)) {
                    writer.write(htmlContent);
                }
                System.out.println("   💾 Saved HTML body to: " + htmlFile);
                
                // Count links in HTML
                int linkCount = countLinks(htmlContent);
                System.out.println("   🔗 Links in body: " + linkCount);
                
                if (linkCount > 0) {
                    extractLinks(htmlContent, htmlFile);
                }
            }
            
        } catch (Exception e) {
            System.out.println("   ⚠️  JSON extraction error: " + e.getMessage());
        }
    }
}
