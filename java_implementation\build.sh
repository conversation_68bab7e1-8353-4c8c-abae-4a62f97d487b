#!/bin/bash

echo "🔨 Building Java Confluence Client..."
echo

# Check if Java is available
if ! command -v java &> /dev/null; then
    echo "❌ Java is not installed or not in PATH"
    echo "Please install Java JDK 8 or higher"
    exit 1
fi

if ! command -v javac &> /dev/null; then
    echo "❌ Java compiler (javac) is not installed or not in PATH"
    echo "Please install Java JDK 8 or higher"
    exit 1
fi

# Show Java version
echo "☕ Java version:"
java -version
echo

# Compile the Java file
echo "📦 Compiling ConfluenceClient.java..."
javac ConfluenceClient.java

if [ $? -ne 0 ]; then
    echo "❌ Compilation failed"
    exit 1
fi

echo "✅ Compilation successful!"
echo

# Run the client
echo "🚀 Running Confluence Client..."
echo
java ConfluenceClient

echo
echo "📁 Generated files:"
ls -la *.html *.json *.txt 2>/dev/null || echo "No output files generated"

echo
echo "✅ Java client execution completed!"
