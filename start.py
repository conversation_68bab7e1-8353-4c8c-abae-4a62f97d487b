#!/usr/bin/env python3
"""
Confluence URL Extractor - Start Script
========================================

This script provides an easy way to start the Confluence URL Extractor
with proper initialization, dependency checking, and user guidance.

Usage:
    python start.py
    python start.py --quick-start
    python start.py --check-only
"""

import sys
import os
import subprocess
from pathlib import Path
import importlib.util

# Color codes for terminal output
class Colors:
    GREEN = '\033[92m'
    RED = '\033[91m'
    YELLOW = '\033[93m'
    BLUE = '\033[94m'
    BOLD = '\033[1m'
    END = '\033[0m'

def print_colored(message, color=Colors.END):
    """Print colored message to terminal"""
    print(f"{color}{message}{Colors.END}")

def print_header():
    """Print application header"""
    print_colored("=" * 60, Colors.BLUE)
    print_colored("🔗 CONFLUENCE URL EXTRACTOR", Colors.BOLD)
    print_colored("Production-Ready URL Extraction Tool", Colors.BLUE)
    print_colored("=" * 60, Colors.BLUE)
    print()

def check_python_version():
    """Check if Python version is compatible"""
    print_colored("🐍 Checking Python version...", Colors.BLUE)
    
    if sys.version_info < (3, 8):
        print_colored("❌ Python 3.8 or higher is required", Colors.RED)
        print_colored(f"   Current version: {sys.version}", Colors.RED)
        return False
    
    print_colored(f"✅ Python {sys.version.split()[0]} - Compatible", Colors.GREEN)
    return True

def check_dependencies():
    """Check if all required dependencies are installed"""
    print_colored("📦 Checking dependencies...", Colors.BLUE)
    
    required_packages = [
        'requests', 'beautifulsoup4', 'python-dotenv', 
        'click', 'pydantic', 'pydantic_settings', 'lxml'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            if package == 'python-dotenv':
                import dotenv
            elif package == 'beautifulsoup4':
                import bs4
            elif package == 'pydantic_settings':
                import pydantic_settings
            else:
                importlib.import_module(package)
            print_colored(f"   ✅ {package}", Colors.GREEN)
        except ImportError:
            print_colored(f"   ❌ {package}", Colors.RED)
            missing_packages.append(package)
    
    if missing_packages:
        print_colored("\n📥 Installing missing dependencies...", Colors.YELLOW)
        try:
            subprocess.check_call([
                sys.executable, '-m', 'pip', 'install', 
                '-r', 'requirements.txt'
            ])
            print_colored("✅ Dependencies installed successfully", Colors.GREEN)
            return True
        except subprocess.CalledProcessError:
            print_colored("❌ Failed to install dependencies", Colors.RED)
            print_colored("   Please run: pip install -r requirements.txt", Colors.YELLOW)
            return False
    
    print_colored("✅ All dependencies are installed", Colors.GREEN)
    return True

def check_configuration():
    """Check if configuration is set up"""
    print_colored("⚙️  Checking configuration...", Colors.BLUE)
    
    env_file = Path('.env')
    if env_file.exists():
        print_colored("✅ Configuration file (.env) found", Colors.GREEN)
        return True
    else:
        print_colored("⚠️  No configuration file found", Colors.YELLOW)
        print_colored("   You can create one using: python main.py setup", Colors.YELLOW)
        return False

def run_tests():
    """Run unit tests to verify installation"""
    print_colored("🧪 Running tests...", Colors.BLUE)
    
    try:
        result = subprocess.run([
            sys.executable, '-m', 'unittest', 
            'test_confluence_extractor.py', '-v'
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print_colored("✅ All tests passed", Colors.GREEN)
            return True
        else:
            print_colored("❌ Some tests failed", Colors.RED)
            print_colored("Test output:", Colors.YELLOW)
            print(result.stdout)
            print(result.stderr)
            return False
    except Exception as e:
        print_colored(f"❌ Error running tests: {str(e)}", Colors.RED)
        return False

def show_usage_examples():
    """Show usage examples"""
    print_colored("\n📖 USAGE EXAMPLES", Colors.BOLD)
    print_colored("-" * 40, Colors.BLUE)
    
    examples = [
        ("Interactive Setup", "python main.py setup"),
        ("Test Connection", "python main.py test-connection"),
        ("Extract URLs", "python main.py extract --page-id 123456789"),
        ("Extract with Options", "python main.py extract --page-id 123456789 --output-dir my_reports"),
        ("Show Help", "python main.py --help"),
        ("Show Version", "python main.py version")
    ]
    
    for description, command in examples:
        print_colored(f"  {description}:", Colors.BLUE)
        print_colored(f"    {command}", Colors.GREEN)
        print()

def show_next_steps():
    """Show recommended next steps"""
    print_colored("🚀 NEXT STEPS", Colors.BOLD)
    print_colored("-" * 40, Colors.BLUE)
    
    steps = [
        "1. Set up configuration: python main.py setup",
        "2. Test your connection: python main.py test-connection", 
        "3. Extract URLs from a page: python main.py extract --page-id YOUR_PAGE_ID",
        "4. Check the reports/ directory for output files"
    ]
    
    for step in steps:
        print_colored(f"  {step}", Colors.GREEN)
    
    print()
    print_colored("📚 For detailed documentation, see README.md", Colors.BLUE)

def main():
    """Main start script function"""
    import argparse
    
    parser = argparse.ArgumentParser(description='Start Confluence URL Extractor')
    parser.add_argument('--quick-start', action='store_true', 
                       help='Skip tests and go straight to setup')
    parser.add_argument('--check-only', action='store_true',
                       help='Only check dependencies and configuration')
    
    args = parser.parse_args()
    
    print_header()
    
    # Check Python version
    if not check_python_version():
        sys.exit(1)
    
    print()
    
    # Check dependencies
    if not check_dependencies():
        sys.exit(1)
    
    print()
    
    # Check configuration
    config_exists = check_configuration()
    
    print()
    
    # Run tests unless quick start
    if not args.quick_start and not args.check_only:
        if not run_tests():
            print_colored("⚠️  Tests failed, but you can still proceed", Colors.YELLOW)
    
    if args.check_only:
        print_colored("✅ System check complete", Colors.GREEN)
        return
    
    print()
    
    # Show usage examples
    show_usage_examples()
    
    # Show next steps
    show_next_steps()
    
    # Offer to run setup if no config
    if not config_exists:
        print_colored("🔧 Would you like to run the setup now? (y/n): ", Colors.YELLOW, end='')
        try:
            response = input().strip().lower()
            if response in ['y', 'yes']:
                print_colored("\n🚀 Starting setup...", Colors.GREEN)
                os.system(f"{sys.executable} main.py setup")
        except KeyboardInterrupt:
            print_colored("\n\n👋 Setup cancelled. You can run it later with: python main.py setup", Colors.YELLOW)
    
    print_colored("\n🎉 Confluence URL Extractor is ready to use!", Colors.GREEN)

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print_colored("\n\n👋 Startup cancelled by user", Colors.YELLOW)
        sys.exit(0)
    except Exception as e:
        print_colored(f"\n❌ Startup error: {str(e)}", Colors.RED)
        sys.exit(1)
