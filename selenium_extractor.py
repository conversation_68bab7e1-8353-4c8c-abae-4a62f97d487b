#!/usr/bin/env python3
"""
Selenium-based URL extractor for JavaScript-heavy Confluence pages
"""
import time
import re
from typing import List, Optional
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, WebDriverException

class SeleniumConfluenceExtractor:
    """Extract URLs from Confluence pages using Selenium for JavaScript execution"""
    
    def __init__(self, headless: bool = True, timeout: int = 30):
        """
        Initialize the Selenium extractor
        
        Args:
            headless: Run browser in headless mode
            timeout: Page load timeout in seconds
        """
        self.headless = headless
        self.timeout = timeout
        self.driver = None
    
    def __enter__(self):
        """Context manager entry"""
        self._setup_driver()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit"""
        self._cleanup_driver()
    
    def _setup_driver(self):
        """Setup Chrome WebDriver with appropriate options"""
        try:
            chrome_options = Options()
            
            if self.headless:
                chrome_options.add_argument("--headless")
            
            # Additional options for better compatibility
            chrome_options.add_argument("--no-sandbox")
            chrome_options.add_argument("--disable-dev-shm-usage")
            chrome_options.add_argument("--disable-gpu")
            chrome_options.add_argument("--window-size=1920,1080")
            chrome_options.add_argument("--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36")
            
            self.driver = webdriver.Chrome(options=chrome_options)
            self.driver.set_page_load_timeout(self.timeout)
            
            print(f"✅ Chrome WebDriver initialized (headless: {self.headless})")
            
        except Exception as e:
            print(f"❌ Failed to setup Chrome WebDriver: {str(e)}")
            print("💡 Make sure Chrome and ChromeDriver are installed")
            raise
    
    def _cleanup_driver(self):
        """Clean up WebDriver resources"""
        if self.driver:
            try:
                self.driver.quit()
                print("✅ WebDriver cleaned up")
            except:
                pass
    
    def extract_urls_from_page(self, page_url: str, username: str, api_token: str) -> List[dict]:
        """
        Extract URLs from a Confluence page using Selenium
        
        Args:
            page_url: Full Confluence page URL
            username: Confluence username
            api_token: Confluence API token
            
        Returns:
            List of URL dictionaries with metadata
        """
        if not self.driver:
            raise RuntimeError("WebDriver not initialized. Use context manager.")
        
        try:
            print(f"🌐 Loading page: {page_url}")
            
            # Navigate to the page
            self.driver.get(page_url)
            
            # Check if we need to login
            current_url = self.driver.current_url
            if "login" in current_url.lower() or "id.atlassian.com" in current_url:
                print("🔐 Login required - attempting authentication...")
                success = self._handle_login(username, api_token)
                if not success:
                    print("❌ Authentication failed")
                    return []
                
                # Navigate back to the original page
                print(f"🔄 Redirecting to original page...")
                self.driver.get(page_url)
            
            # Wait for page content to load
            print("⏳ Waiting for page content to load...")
            try:
                # Wait for main content area to be present
                WebDriverWait(self.driver, self.timeout).until(
                    EC.presence_of_element_located((By.TAG_NAME, "main"))
                )
                print("✅ Main content area loaded")
            except TimeoutException:
                print("⚠️  Main content timeout, trying alternative selectors...")
                try:
                    # Try alternative content selectors
                    WebDriverWait(self.driver, 10).until(
                        EC.presence_of_element_located((By.CLASS_NAME, "wiki-content"))
                    )
                    print("✅ Wiki content loaded")
                except TimeoutException:
                    print("⚠️  Content loading timeout, proceeding anyway...")
            
            # Additional wait for JavaScript to execute
            time.sleep(3)
            
            # Get page source after JavaScript execution
            page_source = self.driver.page_source
            print(f"📄 Page source retrieved: {len(page_source):,} characters")
            
            # Extract URLs from the rendered page
            urls = self._extract_urls_from_html(page_source, page_url)
            
            print(f"🔗 Extracted {len(urls)} URLs from rendered page")
            return urls
            
        except WebDriverException as e:
            print(f"❌ WebDriver error: {str(e)}")
            return []
        except Exception as e:
            print(f"❌ Unexpected error: {str(e)}")
            return []
    
    def _handle_login(self, username: str, api_token: str) -> bool:
        """
        Handle Confluence login if required
        
        Args:
            username: Confluence username
            api_token: Confluence API token
            
        Returns:
            True if login successful, False otherwise
        """
        try:
            # Look for username field
            username_field = WebDriverWait(self.driver, 10).until(
                EC.presence_of_element_located((By.ID, "username"))
            )
            
            # Enter username
            username_field.clear()
            username_field.send_keys(username)
            
            # Click continue/next button
            continue_button = self.driver.find_element(By.ID, "login-submit")
            continue_button.click()
            
            # Wait for password field
            password_field = WebDriverWait(self.driver, 10).until(
                EC.presence_of_element_located((By.ID, "password"))
            )
            
            # Enter API token as password
            password_field.clear()
            password_field.send_keys(api_token)
            
            # Submit login
            login_button = self.driver.find_element(By.ID, "login-submit")
            login_button.click()
            
            # Wait for redirect or success
            WebDriverWait(self.driver, 15).until(
                lambda driver: "login" not in driver.current_url.lower()
            )
            
            print("✅ Login successful")
            return True
            
        except Exception as e:
            print(f"❌ Login failed: {str(e)}")
            return False
    
    def _extract_urls_from_html(self, html_content: str, base_url: str) -> List[dict]:
        """
        Extract URLs from HTML content
        
        Args:
            html_content: HTML content to parse
            base_url: Base URL for resolving relative links
            
        Returns:
            List of URL dictionaries
        """
        urls = []
        
        # Find all links
        link_pattern = r'<a[^>]*href=["\']([^"\']*)["\'][^>]*>(.*?)</a>'
        matches = re.findall(link_pattern, html_content, re.DOTALL | re.IGNORECASE)
        
        for href, text in matches:
            # Clean up text
            text = re.sub(r'<[^>]+>', '', text).strip()
            text = ' '.join(text.split())  # Normalize whitespace
            
            # Skip empty or invalid URLs
            if not href or href.startswith('#') or href.startswith('javascript:'):
                continue
            
            # Resolve relative URLs
            if href.startswith('/'):
                from urllib.parse import urljoin
                full_url = urljoin(base_url, href)
            else:
                full_url = href
            
            # Categorize URL
            if full_url.startswith('mailto:'):
                link_type = 'email'
            elif 'atlassian.net' in full_url:
                link_type = 'internal'
            elif full_url.startswith('http'):
                link_type = 'external'
            else:
                link_type = 'other'
            
            urls.append({
                'url': full_url,
                'text': text[:100],  # Limit text length
                'type': link_type,
                'href': href
            })
        
        return urls

def test_selenium_extractor():
    """Test the Selenium extractor"""
    page_url = "https://sdecloud.atlassian.net/wiki/spaces/affin/pages/837091329/Environment+Info+-+Azure"
    username = input("Enter username: ").strip()
    api_token = input("Enter API token: ").strip()
    
    print(f"🧪 Testing Selenium extractor...")
    print(f"   Page: {page_url}")
    print(f"   Username: {username}")
    print()
    
    try:
        with SeleniumConfluenceExtractor(headless=False) as extractor:  # Set to False to see browser
            urls = extractor.extract_urls_from_page(page_url, username, api_token)
            
            if urls:
                print(f"\n🎉 Successfully extracted {len(urls)} URLs:")
                for i, url in enumerate(urls[:20], 1):  # Show first 20
                    print(f"   {i:2d}. [{url['type']:8s}] {url['url']}")
                    if url['text']:
                        print(f"       Text: {url['text']}")
                
                # Save results
                import json
                with open('selenium_extracted_urls.json', 'w', encoding='utf-8') as f:
                    json.dump(urls, f, indent=2, ensure_ascii=False)
                print(f"\n💾 Saved results to: selenium_extracted_urls.json")
                
            else:
                print(f"\n❌ No URLs extracted")
                
    except Exception as e:
        print(f"❌ Test failed: {str(e)}")

if __name__ == "__main__":
    test_selenium_extractor()
