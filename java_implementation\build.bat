@echo off
echo 🔨 Building Java Confluence Client...
echo.

REM Check if Java is available
java -version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Java is not installed or not in PATH
    echo Please install Java JDK 8 or higher
    pause
    exit /b 1
)

REM Compile the Java file
echo 📦 Compiling ConfluenceClient.java...
javac ConfluenceClient.java

if %errorlevel% neq 0 (
    echo ❌ Compilation failed
    pause
    exit /b 1
)

echo ✅ Compilation successful!
echo.

REM Run the client
echo 🚀 Running Confluence Client...
echo.
java ConfluenceClient

echo.
echo 📁 Check the generated files in this directory:
dir *.html *.json *.txt 2>nul

pause
