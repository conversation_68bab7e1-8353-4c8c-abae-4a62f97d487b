import java.io.*;
import java.net.*;
import java.nio.charset.StandardCharsets;
import java.util.Base64;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Simple Java client to test Confluence API access and save page content
 */
public class ConfluenceClient {
    
    private static final String CONFLUENCE_URL = "https://sdecloud.atlassian.net";
    private static final String USERNAME = "imitiyaz";
    private static final String API_TOKEN = "ATATT3xFfGF0hpe-8j9m0miV0UUVy6JaMzsgbXht2AVRI7mPn9sekwyqo9v8yPpx9Ee3kyj8LWWiC9usd0GBSa3Wb0etrIvmqnvNf5oqRiP379Jf1me5DhggNQ_763UlHA0pGnnE6zXIHVOupY_sytIeX756FPaHoGA_pfmzRbHhi7ZPcswMvTak=6D360167";
    private static final String PAGE_ID = "837091329";
    
    public static void main(String[] args) {
        ConfluenceClient client = new ConfluenceClient();
        
        System.out.println("🔍 Java Confluence Client - Testing API Access");
        System.out.println("=" + "=".repeat(50));
        System.out.println("Confluence URL: " + CONFLUENCE_URL);
        System.out.println("Username: " + USERNAME);
        System.out.println("Page ID: " + PAGE_ID);
        System.out.println();
        
        // Test different API endpoints
        client.testApiEndpoints();
        
        // Test direct page access
        client.testDirectPageAccess();
    }
    
    /**
     * Test various API endpoints to find working ones
     */
    public void testApiEndpoints() {
        System.out.println("🧪 Testing API Endpoints");
        System.out.println("-".repeat(30));
        
        String[] endpoints = {
            "/rest/api/user/current",
            "/wiki/rest/api/space",
            "/wiki/rest/api/content",
            "/rest/api/content/" + PAGE_ID,
            "/wiki/rest/api/content/" + PAGE_ID,
            "/wiki/rest/api/content/" + PAGE_ID + "?expand=body.storage",
            "/wiki/rest/api/content/search?cql=id=" + PAGE_ID,
            "/wiki/api/v2/pages/" + PAGE_ID,
            "/wiki/api/v2/pages/" + PAGE_ID + "?body-format=storage",
            "/wiki/rest/api/content?spaceKey=affin",
            "/wiki/rest/api/content?spaceKey=affin&expand=body.storage"
        };
        
        for (String endpoint : endpoints) {
            testEndpoint(endpoint);
        }
        
        System.out.println();
    }
    
    /**
     * Test a specific API endpoint
     */
    private void testEndpoint(String endpoint) {
        try {
            String url = CONFLUENCE_URL + endpoint;
            System.out.printf("📡 Testing: %s%n", endpoint);
            
            HttpURLConnection connection = createConnection(url);
            int responseCode = connection.getResponseCode();
            
            System.out.printf("   Status: %d%n", responseCode);
            
            if (responseCode == 200) {
                String response = readResponse(connection);
                System.out.printf("   ✅ SUCCESS! Response length: %d chars%n", response.length());
                
                // Check if it contains page content
                if (response.contains("\"title\"") || response.contains("\"body\"")) {
                    System.out.println("   📄 Contains page data");
                    
                    // Save successful response
                    String filename = "response_" + endpoint.replaceAll("[^a-zA-Z0-9]", "_") + ".json";
                    saveToFile(filename, response);
                    System.out.printf("   💾 Saved to: %s%n", filename);
                    
                    // Check for body content with links
                    if (response.contains("\"body\"") && response.contains("storage")) {
                        System.out.println("   🔗 Contains body content - checking for links...");
                        extractAndSaveLinks(response, endpoint);
                    }
                }
            } else if (responseCode == 401) {
                System.out.println("   ❌ Authentication failed");
            } else if (responseCode == 403) {
                System.out.println("   ❌ Access forbidden");
            } else if (responseCode == 404) {
                System.out.println("   ❌ Not found");
            } else {
                String errorResponse = readErrorResponse(connection);
                System.out.printf("   ❌ Error: %s%n", errorResponse.substring(0, Math.min(100, errorResponse.length())));
            }
            
        } catch (Exception e) {
            System.out.printf("   ❌ Exception: %s%n", e.getMessage());
        }
        
        System.out.println();
    }
    
    /**
     * Test direct page access (HTML)
     */
    public void testDirectPageAccess() {
        System.out.println("🌐 Testing Direct Page Access");
        System.out.println("-".repeat(30));
        
        String[] pageUrls = {
            CONFLUENCE_URL + "/wiki/spaces/affin/pages/" + PAGE_ID,
            CONFLUENCE_URL + "/wiki/spaces/affin/pages/" + PAGE_ID + "/Environment+Info+-+Azure"
        };
        
        for (String pageUrl : pageUrls) {
            testDirectPage(pageUrl);
        }
    }
    
    /**
     * Test direct page access
     */
    private void testDirectPage(String pageUrl) {
        try {
            System.out.printf("🔗 Testing: %s%n", pageUrl);
            
            HttpURLConnection connection = createConnection(pageUrl);
            int responseCode = connection.getResponseCode();
            
            System.out.printf("   Status: %d%n", responseCode);
            
            if (responseCode == 200) {
                String response = readResponse(connection);
                System.out.printf("   ✅ SUCCESS! Response length: %d chars%n", response.length());
                
                // Save HTML content
                String filename = "direct_page_" + PAGE_ID + ".html";
                saveToFile(filename, response);
                System.out.printf("   💾 Saved to: %s%n", filename);
                
                // Count links in HTML
                int linkCount = countLinksInHtml(response);
                System.out.printf("   🔗 Links found: %d%n", linkCount);
                
                if (linkCount > 0) {
                    extractLinksFromHtml(response);
                }
                
            } else {
                String errorResponse = readErrorResponse(connection);
                System.out.printf("   ❌ Error %d: %s%n", responseCode, 
                    errorResponse.substring(0, Math.min(200, errorResponse.length())));
            }
            
        } catch (Exception e) {
            System.out.printf("   ❌ Exception: %s%n", e.getMessage());
        }
        
        System.out.println();
    }
    
    /**
     * Create HTTP connection with authentication
     */
    private HttpURLConnection createConnection(String url) throws IOException {
        URL urlObj = new URL(url);
        HttpURLConnection connection = (HttpURLConnection) urlObj.openConnection();
        
        // Set basic authentication
        String auth = USERNAME + ":" + API_TOKEN;
        String encodedAuth = Base64.getEncoder().encodeToString(auth.getBytes(StandardCharsets.UTF_8));
        connection.setRequestProperty("Authorization", "Basic " + encodedAuth);
        
        // Set headers
        connection.setRequestProperty("Accept", "application/json");
        connection.setRequestProperty("User-Agent", "Java-ConfluenceClient/1.0");
        
        // Set timeouts
        connection.setConnectTimeout(10000);
        connection.setReadTimeout(30000);
        
        return connection;
    }
    
    /**
     * Read successful response
     */
    private String readResponse(HttpURLConnection connection) throws IOException {
        try (BufferedReader reader = new BufferedReader(
                new InputStreamReader(connection.getInputStream(), StandardCharsets.UTF_8))) {
            StringBuilder response = new StringBuilder();
            String line;
            while ((line = reader.readLine()) != null) {
                response.append(line).append("\n");
            }
            return response.toString();
        }
    }
    
    /**
     * Read error response
     */
    private String readErrorResponse(HttpURLConnection connection) {
        try (BufferedReader reader = new BufferedReader(
                new InputStreamReader(connection.getErrorStream(), StandardCharsets.UTF_8))) {
            StringBuilder response = new StringBuilder();
            String line;
            while ((line = reader.readLine()) != null) {
                response.append(line).append("\n");
            }
            return response.toString();
        } catch (Exception e) {
            return "Could not read error response: " + e.getMessage();
        }
    }
    
    /**
     * Save content to file
     */
    private void saveToFile(String filename, String content) {
        try (FileWriter writer = new FileWriter(filename, StandardCharsets.UTF_8)) {
            writer.write(content);
        } catch (IOException e) {
            System.out.printf("   ⚠️  Failed to save file: %s%n", e.getMessage());
        }
    }
    
    /**
     * Count links in HTML content
     */
    private int countLinksInHtml(String html) {
        Pattern linkPattern = Pattern.compile("<a[^>]*href=[\"']([^\"']*)[\"'][^>]*>", Pattern.CASE_INSENSITIVE);
        Matcher matcher = linkPattern.matcher(html);
        int count = 0;
        while (matcher.find()) {
            count++;
        }
        return count;
    }
    
    /**
     * Extract and display links from HTML
     */
    private void extractLinksFromHtml(String html) {
        System.out.println("   📋 Extracting links from HTML...");
        
        Pattern linkPattern = Pattern.compile("<a[^>]*href=[\"']([^\"']*)[\"'][^>]*>(.*?)</a>", 
            Pattern.CASE_INSENSITIVE | Pattern.DOTALL);
        Matcher matcher = linkPattern.matcher(html);
        
        int count = 0;
        StringBuilder linksList = new StringBuilder();
        linksList.append("Extracted Links:\n");
        linksList.append("=" + "=".repeat(50) + "\n");
        
        while (matcher.find() && count < 20) { // Limit to first 20 links
            String href = matcher.group(1);
            String text = matcher.group(2).replaceAll("<[^>]+>", "").trim(); // Remove HTML tags
            
            if (text.length() > 50) {
                text = text.substring(0, 50) + "...";
            }
            
            count++;
            String linkInfo = String.format("%2d. %s\n    Text: %s\n", count, href, text);
            System.out.printf("      %d. %s%n", count, href);
            linksList.append(linkInfo).append("\n");
        }
        
        if (count > 0) {
            saveToFile("extracted_links_" + PAGE_ID + ".txt", linksList.toString());
            System.out.printf("   💾 Saved %d links to: extracted_links_%s.txt%n", count, PAGE_ID);
        }
    }
    
    /**
     * Extract and save links from JSON response
     */
    private void extractAndSaveLinks(String jsonResponse, String endpoint) {
        // Simple regex to find HTML content in JSON
        Pattern bodyPattern = Pattern.compile("\"value\"\\s*:\\s*\"(.*?)\"", Pattern.DOTALL);
        Matcher matcher = bodyPattern.matcher(jsonResponse);
        
        if (matcher.find()) {
            String htmlContent = matcher.group(1);
            // Unescape JSON string
            htmlContent = htmlContent.replace("\\\"", "\"")
                                   .replace("\\n", "\n")
                                   .replace("\\r", "\r")
                                   .replace("\\t", "\t")
                                   .replace("\\\\", "\\");
            
            System.out.printf("   📄 Found HTML content: %d chars%n", htmlContent.length());
            
            // Save HTML content
            String htmlFilename = "api_content_" + endpoint.replaceAll("[^a-zA-Z0-9]", "_") + ".html";
            saveToFile(htmlFilename, htmlContent);
            System.out.printf("   💾 Saved HTML to: %s%n", htmlFilename);
            
            // Count and extract links
            int linkCount = countLinksInHtml(htmlContent);
            System.out.printf("   🔗 Links in API content: %d%n", linkCount);
            
            if (linkCount > 0) {
                extractLinksFromHtml(htmlContent);
            }
        }
    }
}
