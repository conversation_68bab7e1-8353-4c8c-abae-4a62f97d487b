#!/usr/bin/env python3
"""
Helper tool to find accessible Confluence pages
"""
import sys
import click
from config import ConfluenceConfig
from confluence_client import ConfluenceClient
from logger import setup_logger

@click.command()
@click.option('--url', help='Confluence instance URL')
@click.option('--username', help='Confluence username or email')
@click.option('--api-token', help='Confluence API token')
@click.option('--space-key', help='Specific space to search (optional)')
@click.option('--search-term', help='Search term to find pages (optional)')
def find_pages(url, username, api_token, space_key, search_term):
    """Find accessible Confluence pages"""
    
    # Setup logging
    logger = setup_logger(level="INFO")
    
    # Prompt for missing parameters
    if not url:
        url = click.prompt('Confluence URL (e.g., https://company.atlassian.net)')
    if not username:
        username = click.prompt('Username/Email')
    if not api_token:
        api_token = click.prompt('API Token', hide_input=True)
    
    try:
        # Create configuration
        config = ConfluenceConfig(
            confluence_url=url,
            username=username,
            api_token=api_token
        )
        
        click.echo("\n🔍 Finding accessible Confluence pages...")
        
        with ConfluenceClient(config) as client:
            # Test connection
            if not client.test_connection():
                click.echo("❌ Failed to connect to Confluence")
                sys.exit(1)
            
            # Get accessible spaces
            click.echo("\n📁 Accessible spaces:")
            try:
                response = client._make_request('GET', '/wiki/rest/api/space', params={'limit': 50})
                spaces_data = response.json()
                spaces = spaces_data.get('results', [])
                
                if not spaces:
                    click.echo("   No accessible spaces found")
                    return
                
                for space in spaces:
                    space_key_val = space.get('key', 'N/A')
                    space_name = space.get('name', 'N/A')
                    click.echo(f"   📂 {space_key_val}: {space_name}")
                
                # If specific space requested, search in that space
                if space_key:
                    target_spaces = [space_key]
                else:
                    target_spaces = [space.get('key') for space in spaces if space.get('key')]
                
                # Search for pages in each space
                click.echo(f"\n📄 Pages with content:")
                total_pages_found = 0
                
                for space_key_val in target_spaces:
                    try:
                        # Get pages from space
                        params = {
                            'spaceKey': space_key_val,
                            'expand': 'body.storage',
                            'limit': 20
                        }
                        
                        if search_term:
                            # Use search if term provided
                            endpoint = '/wiki/rest/api/content/search'
                            params = {
                                'cql': f'space={space_key_val} AND text~"{search_term}"',
                                'expand': 'body.storage',
                                'limit': 20
                            }
                        else:
                            endpoint = '/wiki/rest/api/content'
                        
                        response = client._make_request('GET', endpoint, params=params)
                        data = response.json()
                        pages = data.get('results', [])
                        
                        pages_with_content = []
                        for page in pages:
                            page_id = page.get('id', 'N/A')
                            title = page.get('title', 'N/A')
                            page_type = page.get('type', 'N/A')
                            
                            # Check if page has content
                            content = ""
                            if page.get('body', {}).get('storage', {}).get('value'):
                                content = page['body']['storage']['value']
                            
                            if content and len(content) > 100:  # Only show pages with substantial content
                                pages_with_content.append({
                                    'id': page_id,
                                    'title': title,
                                    'type': page_type,
                                    'content_length': len(content),
                                    'space': space_key_val
                                })
                        
                        if pages_with_content:
                            click.echo(f"\n   📂 Space: {space_key_val}")
                            for page in pages_with_content:
                                click.echo(f"      📄 ID: {page['id']}")
                                click.echo(f"         Title: {page['title']}")
                                click.echo(f"         Type: {page['type']}")
                                click.echo(f"         Content: {page['content_length']:,} characters")
                                click.echo(f"         Extract command: python main.py extract --page-id \"{page['id']}\" --space-key \"{page['space']}\"")
                                click.echo()
                                total_pages_found += 1
                    
                    except Exception as e:
                        click.echo(f"   ⚠️  Could not access space {space_key_val}: {str(e)}")
                
                if total_pages_found == 0:
                    click.echo("   No pages with substantial content found")
                    click.echo("\n💡 Try:")
                    click.echo("   1. Check if you have access to the correct Confluence instance")
                    click.echo("   2. Verify your API token has the necessary permissions")
                    click.echo("   3. Contact your Confluence administrator for access")
                else:
                    click.echo(f"\n✅ Found {total_pages_found} pages with content")
                    click.echo("\n💡 To extract URLs from any page, use the provided extract commands above")
                
            except Exception as e:
                click.echo(f"❌ Error accessing spaces: {str(e)}")
                sys.exit(1)
    
    except Exception as e:
        click.echo(f"❌ Error: {str(e)}")
        sys.exit(1)

if __name__ == '__main__':
    find_pages()
