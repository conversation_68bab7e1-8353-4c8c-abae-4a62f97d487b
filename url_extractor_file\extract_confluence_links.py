import os
from bs4 import BeautifulSoup
import urllib.parse
import json
from datetime import datetime

def extract_links_from_html(file_path):
    """
    Extract all links from a Confluence HTML export
    Returns a list of dictionaries with link details
    """
    if not os.path.exists(file_path):
        print(f"Error: File not found at {file_path}")
        return []
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            soup = BeautifulSoup(f.read(), 'html.parser')
    except Exception as e:
        print(f"Error reading HTML file: {str(e)}")
        return []
    
    links = []
    base_url = None
    
    # Try to extract the base URL from <head><base> tag if it exists
    base_tag = soup.find('base', href=True)
    if base_tag:
        base_url = base_tag['href']
        print(f"Found base URL: {base_url}")
    
    # Extract all links
    for a_tag in soup.find_all('a', href=True):
        href = a_tag['href'].strip()
        text = a_tag.get_text(strip=True) or href
        
        # Skip unwanted links
        if href.startswith(('#', 'javascript:', 'mailto:')):
            continue
        
        # Handle relative URLs if we have a base URL
        if base_url and not urllib.parse.urlparse(href).netloc:
            href = urllib.parse.urljoin(base_url, href)
        
        # Get link type/class if available
        link_class = ' '.join(a_tag.get('class', [])) if a_tag.get('class') else None
        
        links.append({
            'text': text,
            'url': href,
            # 'element_id': a_tag.get('id'),
            # 'class': link_class,
            'title': a_tag.get('title'),
            #'source_element': str(a_tag) if len(str(a_tag)) < 100 else str(a_tag)[:100] + "..."
        })
    
    return links

def save_links_to_files(links, output_prefix='confluence_links'):
    """Save extracted links to multiple file formats"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    # Save to JSON
    json_file = f"{output_prefix}_{timestamp}.json"
    with open(json_file, 'w', encoding='utf-8') as f:
        json.dump(links, f, indent=2, ensure_ascii=False)
    print(f"Saved {len(links)} links to {json_file}")
    
    # Save to Markdown
    md_file = f"{output_prefix}_{timestamp}.md"
    with open(md_file, 'w', encoding='utf-8') as f:
        f.write(f"# Extracted Links ({len(links)} total)\n\n")
        for link in links:
            f.write(f"- [{link['text']}]({link['url']})")
            if link['class']:
                f.write(f" (class: {link['class']})")
            f.write("\n")
    print(f"Saved markdown list to {md_file}")
    
    # Save to CSV
    csv_file = f"{output_prefix}_{timestamp}.csv"
    with open(csv_file, 'w', encoding='utf-8') as f:
        f.write("Text,URL,Class,Title,Element ID\n")
        for link in links:
            f.write(f'"{link["text"]}","{link["url"]}","{link["class"] or ""}","{link["title"] or ""}","{link["element_id"] or ""}"\n')
    print(f"Saved CSV to {csv_file}")

if __name__ == "__main__":
    # Configuration - change this to your HTML file path
    HTML_FILE_PATH = "Pg1_Affin.html"  # Update with your actual file path
    
    print(f"Extracting links from: {HTML_FILE_PATH}")
    links = extract_links_from_html(HTML_FILE_PATH)
    
    if links:
        print(f"\nFound {len(links)} links:")
        print("Sample links:")
        for i, link in enumerate(links[:5], 1):
            print(f"{i}. {link['text']} → {link['url']}")
        
        if len(links) > 5:
            print(f"... plus {len(links)-5} more links")
        
        # Save results to multiple formats
        save_links_to_files(links)
        
        print("\nAdditional options you might want to add:")
        print("- Filter by specific link classes")
        print("- Remove duplicate URLs")
        print("- Categorize links by domain")
    else:
        print("No links found in the HTML file. Possible reasons:")
        print("- The file doesn't contain any links")
        print("- The file wasn't properly saved from Confluence")
        print("- The HTML structure is different than expected")