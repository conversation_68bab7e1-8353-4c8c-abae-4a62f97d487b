{"results": [{"id": "847642818", "type": "page", "ari": "ari:cloud:confluence:ea0bd99c-e024-4402-bb30-d38a477aab6e:page/847642818", "base64EncodedAri": "YXJpOmNsb3VkOmNvbmZsdWVuY2U6ZWEwYmQ5OWMtZTAyNC00NDAyLWJiMzAtZDM4YTQ3N2FhYjZlOmNvbnRlbnQvODQ3NjQyODE4", "status": "current", "title": "Logging", "macroRenderedOutput": {}, "extensions": {"position": 368}, "_expandable": {"container": "/rest/api/space/MOBIUISINTRO", "metadata": "", "restrictions": "/rest/api/content/847642818/restriction/byOperation", "history": "/rest/api/content/847642818/history", "body": "", "version": "", "descendants": "/rest/api/content/847642818/descendant", "space": "/rest/api/space/MOBIUISINTRO", "childTypes": "", "schedulePublishInfo": "", "operations": "", "schedulePublishDate": "", "children": "/rest/api/content/847642818/child", "ancestors": "", "draftVersion": ""}, "_links": {"self": "https://sdecloud.atlassian.net/wiki/rest/api/content/847642818", "tinyui": "/x/wgCGMg", "editui": "/pages/resumedraft.action?draftId=847642818", "webui": "/spaces/MOBIUISINTRO/pages/847642818/Logging", "edituiv2": "/spaces/MOBIUISINTRO/pages/edit-v2/847642818"}}, {"id": "847675720", "type": "page", "ari": "ari:cloud:confluence:ea0bd99c-e024-4402-bb30-d38a477aab6e:page/847675720", "base64EncodedAri": "YXJpOmNsb3VkOmNvbmZsdWVuY2U6ZWEwYmQ5OWMtZTAyNC00NDAyLWJiMzAtZDM4YTQ3N2FhYjZlOmNvbnRlbnQvODQ3Njc1NzIw", "status": "current", "title": "What is Mobius", "macroRenderedOutput": {}, "extensions": {"position": 48112777}, "_expandable": {"container": "/rest/api/space/MOBIUISINTRO", "metadata": "", "restrictions": "/rest/api/content/847675720/restriction/byOperation", "history": "/rest/api/content/847675720/history", "body": "", "version": "", "descendants": "/rest/api/content/847675720/descendant", "space": "/rest/api/space/MOBIUISINTRO", "childTypes": "", "schedulePublishInfo": "", "operations": "", "schedulePublishDate": "", "children": "/rest/api/content/847675720/child", "ancestors": "", "draftVersion": ""}, "_links": {"self": "https://sdecloud.atlassian.net/wiki/rest/api/content/847675720", "tinyui": "/x/SIGGMg", "editui": "/pages/resumedraft.action?draftId=847675720", "webui": "/spaces/MOBIUISINTRO/pages/847675720/What+is+Mobius", "edituiv2": "/spaces/MOBIUISINTRO/pages/edit-v2/847675720"}}, {"id": "848003243", "type": "page", "ari": "ari:cloud:confluence:ea0bd99c-e024-4402-bb30-d38a477aab6e:page/848003243", "base64EncodedAri": "YXJpOmNsb3VkOmNvbmZsdWVuY2U6ZWEwYmQ5OWMtZTAyNC00NDAyLWJiMzAtZDM4YTQ3N2FhYjZlOmNvbnRlbnQvODQ4MDAzMjQz", "status": "current", "title": "About Mobius", "macroRenderedOutput": {}, "extensions": {"position": 2470}, "_expandable": {"container": "/rest/api/space/MOBIUISINTRO", "metadata": "", "restrictions": "/rest/api/content/848003243/restriction/byOperation", "history": "/rest/api/content/848003243/history", "body": "", "version": "", "descendants": "/rest/api/content/848003243/descendant", "space": "/rest/api/space/MOBIUISINTRO", "childTypes": "", "schedulePublishInfo": "", "operations": "", "schedulePublishDate": "", "children": "/rest/api/content/848003243/child", "ancestors": "", "draftVersion": ""}, "_links": {"self": "https://sdecloud.atlassian.net/wiki/rest/api/content/848003243", "tinyui": "/x/q4CLMg", "editui": "/pages/resumedraft.action?draftId=848003243", "webui": "/spaces/MOBIUISINTRO/pages/848003243/About+Mobius", "edituiv2": "/spaces/MOBIUISINTRO/pages/edit-v2/848003243"}}, {"id": "*********", "type": "page", "ari": "ari:cloud:confluence:ea0bd99c-e024-4402-bb30-d38a477aab6e:page/*********", "base64EncodedAri": "YXJpOmNsb3VkOmNvbmZsdWVuY2U6ZWEwYmQ5OWMtZTAyNC00NDAyLWJiMzAtZDM4YTQ3N2FhYjZlOmNvbnRlbnQvODQ4MDM2MDI1", "status": "current", "title": "Monitor and Alert", "macroRenderedOutput": {}, "extensions": {"position": 3197}, "_expandable": {"container": "/rest/api/space/MOBIUISINTRO", "metadata": "", "restrictions": "/rest/api/content/*********/restriction/byOperation", "history": "/rest/api/content/*********/history", "body": "", "version": "", "descendants": "/rest/api/content/*********/descendant", "space": "/rest/api/space/MOBIUISINTRO", "childTypes": "", "schedulePublishInfo": "", "operations": "", "schedulePublishDate": "", "children": "/rest/api/content/*********/child", "ancestors": "", "draftVersion": ""}, "_links": {"self": "https://sdecloud.atlassian.net/wiki/rest/api/content/*********", "tinyui": "/x/uQCMMg", "editui": "/pages/resumedraft.action?draftId=*********", "webui": "/spaces/MOBIUISINTRO/pages/*********/Monitor+and+<PERSON><PERSON>", "edituiv2": "/spaces/MOBIUISINTRO/pages/edit-v2/*********"}}, {"id": "*********", "type": "page", "ari": "ari:cloud:confluence:ea0bd99c-e024-4402-bb30-d38a477aab6e:page/*********", "base64EncodedAri": "YXJpOmNsb3VkOmNvbmZsdWVuY2U6ZWEwYmQ5OWMtZTAyNC00NDAyLWJiMzAtZDM4YTQ3N2FhYjZlOmNvbnRlbnQvODQ4MTAxNjMx", "status": "current", "title": "Mobius Introduction", "macroRenderedOutput": {}, "extensions": {"position": 945}, "_expandable": {"container": "/rest/api/space/MOBIUISINTRO", "metadata": "", "restrictions": "/rest/api/content/*********/restriction/byOperation", "history": "/rest/api/content/*********/history", "body": "", "version": "", "descendants": "/rest/api/content/*********/descendant", "space": "/rest/api/space/MOBIUISINTRO", "childTypes": "", "schedulePublishInfo": "", "operations": "", "schedulePublishDate": "", "children": "/rest/api/content/*********/child", "ancestors": "", "draftVersion": ""}, "_links": {"self": "https://sdecloud.atlassian.net/wiki/rest/api/content/*********", "tinyui": "/x/-wCNMg", "editui": "/pages/resumedraft.action?draftId=*********", "webui": "/spaces/MOBIUISINTRO/overview", "edituiv2": "/spaces/MOBIUISINTRO/pages/edit-v2/*********"}}, {"id": "*********", "type": "page", "ari": "ari:cloud:confluence:ea0bd99c-e024-4402-bb30-d38a477aab6e:page/*********", "base64EncodedAri": "YXJpOmNsb3VkOmNvbmZsdWVuY2U6ZWEwYmQ5OWMtZTAyNC00NDAyLWJiMzAtZDM4YTQ3N2FhYjZlOmNvbnRlbnQvODQ4MTAxNjQz", "status": "current", "title": "Template - How-to guide", "macroRenderedOutput": {}, "extensions": {"position": 539}, "_expandable": {"container": "/rest/api/space/MOBIUISINTRO", "metadata": "", "restrictions": "/rest/api/content/*********/restriction/byOperation", "history": "/rest/api/content/*********/history", "body": "", "version": "", "descendants": "/rest/api/content/*********/descendant", "space": "/rest/api/space/MOBIUISINTRO", "childTypes": "", "schedulePublishInfo": "", "operations": "", "schedulePublishDate": "", "children": "/rest/api/content/*********/child", "ancestors": "", "draftVersion": ""}, "_links": {"self": "https://sdecloud.atlassian.net/wiki/rest/api/content/*********", "tinyui": "/x/CwGNMg", "editui": "/pages/resumedraft.action?draftId=*********", "webui": "/spaces/MOBIUISINTRO/pages/*********/Template+-+How-to+guide", "edituiv2": "/spaces/MOBIUISINTRO/pages/edit-v2/*********"}}, {"id": "*********", "type": "page", "ari": "ari:cloud:confluence:ea0bd99c-e024-4402-bb30-d38a477aab6e:page/*********", "base64EncodedAri": "YXJpOmNsb3VkOmNvbmZsdWVuY2U6ZWEwYmQ5OWMtZTAyNC00NDAyLWJiMzAtZDM4YTQ3N2FhYjZlOmNvbnRlbnQvODQ4MTAxNjU3", "status": "current", "title": "Template - Troubleshooting article", "macroRenderedOutput": {}, "extensions": {"position": 872}, "_expandable": {"container": "/rest/api/space/MOBIUISINTRO", "metadata": "", "restrictions": "/rest/api/content/*********/restriction/byOperation", "history": "/rest/api/content/*********/history", "body": "", "version": "", "descendants": "/rest/api/content/*********/descendant", "space": "/rest/api/space/MOBIUISINTRO", "childTypes": "", "schedulePublishInfo": "", "operations": "", "schedulePublishDate": "", "children": "/rest/api/content/*********/child", "ancestors": "", "draftVersion": ""}, "_links": {"self": "https://sdecloud.atlassian.net/wiki/rest/api/content/*********", "tinyui": "/x/GQGNMg", "editui": "/pages/resumedraft.action?draftId=*********", "webui": "/spaces/MOBIUISINTRO/pages/*********/Template+-+Troubleshooting+article", "edituiv2": "/spaces/MOBIUISINTRO/pages/edit-v2/*********"}}], "start": 0, "limit": 25, "size": 7, "_links": {"base": "https://sdecloud.atlassian.net/wiki", "context": "/wiki", "self": "https://sdecloud.atlassian.net/wiki/rest/api/content"}}