#!/usr/bin/env python3
"""
Test the search API endpoint that works
"""
import requests
from requests.auth import HTTPBasicAuth
import json
import re

def test_search_api():
    url = 'https://sdecloud.atlassian.net/wiki/rest/api/content/search?cql=id=837091329&expand=body.storage'
    auth = HTTPBasicAuth('imitiyaz', 'ATATT3xFfGF0hpe-8j9m0miV0UUVy6JaMzsgbXht2AVRI7mPn9sekwyqo9v8yPpx9Ee3kyj8LWWiC9usd0GBSa3Wb0etrIvmqnvNf5oqRiP379Jf1me5DhggNQ_763UlHA0pGnnE6zXIHVOupY_sytIeX756FPaHoGA_pfmzRbHhi7ZPcswMvTak=6D360167')

    response = requests.get(url, auth=auth, headers={'Accept': 'application/json'})
    print(f'Status: {response.status_code}')

    if response.status_code == 200:
        data = response.json()
        print(f'Results count: {len(data["results"])}')
        if data['results']:
            page = data['results'][0]
            print(f'Title: {page.get("title", "N/A")}')
            print(f'Type: {page.get("type", "N/A")}')
            if 'body' in page:
                body_formats = list(page['body'].keys())
                print(f'Body formats: {body_formats}')
                if 'storage' in page['body']:
                    content = page['body']['storage']['value']
                    print(f'Content length: {len(content)} chars')
                    
                    # Save content to file
                    with open('page_content.html', 'w', encoding='utf-8') as f:
                        f.write(content)
                    print('Saved content to page_content.html')
                    
                    # Count links with simple regex
                    links = re.findall(r'href=["\']([^"\']*)["\']', content)
                    print(f'Links found: {len(links)}')
                    if links:
                        print(f'Sample links: {links[:10]}')
                        
                        # Categorize links
                        external = [l for l in links if l.startswith('http') and 'sdecloud.atlassian.net' not in l]
                        internal = [l for l in links if 'sdecloud.atlassian.net' in l]
                        relative = [l for l in links if l.startswith('/')]
                        
                        print(f'External links: {len(external)}')
                        print(f'Internal links: {len(internal)}')
                        print(f'Relative links: {len(relative)}')

if __name__ == "__main__":
    test_search_api()
