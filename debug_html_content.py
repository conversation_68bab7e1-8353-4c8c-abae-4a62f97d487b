#!/usr/bin/env python3
"""
Debug script to analyze HTML content and URL extraction
"""
import sys
from pathlib import Path
from bs4 import BeautifulSoup
import re

from config import ConfluenceConfig
from confluence_client import ConfluenceClient
from url_extractor import URLExtractor
from logger import setup_logger

def analyze_html_content():
    """Analyze the HTML content we're getting from Confluence"""
    
    # Setup logging
    logger = setup_logger(level="DEBUG")
    
    print("🔍 HTML Content Analysis Tool")
    print("=" * 50)
    
    # Configuration
    config = ConfluenceConfig(
        confluence_url="https://sdecloud.atlassian.net",
        username="imitiyaz",
        api_token=input("Enter API token: ").strip(),
        page_id="837091329",
        space_key="affin"
    )
    
    try:
        with ConfluenceClient(config) as client:
            print("\n📄 Fetching page content...")
            
            # Get HTML content
            html_content = client.get_page_html_content(
                config.page_id, 
                config.space_key
            )
            
            print(f"📊 HTML Content Stats:")
            print(f"   Total length: {len(html_content):,} characters")
            print(f"   Lines: {html_content.count(chr(10)):,}")
            
            # Save raw HTML for inspection
            html_file = Path("debug_page_content.html")
            with open(html_file, 'w', encoding='utf-8') as f:
                f.write(html_content)
            print(f"   💾 Saved raw HTML to: {html_file}")
            
            # Parse with BeautifulSoup
            print(f"\n🔍 Parsing HTML content...")
            soup = BeautifulSoup(html_content, 'html.parser')
            
            # Find all links
            all_links = soup.find_all('a')
            print(f"   Total <a> tags found: {len(all_links)}")
            
            # Analyze links
            links_with_href = [link for link in all_links if link.get('href')]
            print(f"   Links with href attribute: {len(links_with_href)}")
            
            # Categorize links
            external_links = []
            internal_links = []
            relative_links = []
            other_links = []
            
            print(f"\n📋 Link Analysis:")
            for i, link in enumerate(links_with_href[:20], 1):  # Show first 20
                href = link.get('href', '')
                text = link.get_text(strip=True)[:50]  # First 50 chars
                
                if href.startswith('http'):
                    if 'sdecloud.atlassian.net' in href:
                        internal_links.append((href, text))
                        category = "INTERNAL"
                    else:
                        external_links.append((href, text))
                        category = "EXTERNAL"
                elif href.startswith('/'):
                    relative_links.append((href, text))
                    category = "RELATIVE"
                else:
                    other_links.append((href, text))
                    category = "OTHER"
                
                print(f"   {i:2d}. [{category:8s}] {href[:60]:<60} | {text}")
            
            if len(links_with_href) > 20:
                print(f"   ... and {len(links_with_href) - 20} more links")
            
            print(f"\n📊 Link Categories:")
            print(f"   External links: {len(external_links)}")
            print(f"   Internal links: {len(internal_links)}")
            print(f"   Relative links: {len(relative_links)}")
            print(f"   Other links: {len(other_links)}")
            
            # Test our URL extractor
            print(f"\n🧪 Testing URL Extractor...")
            extractor = URLExtractor(config.confluence_url)
            extracted_urls = extractor.extract_urls_from_html(html_content, config.confluence_url)
            
            print(f"   URLs extracted by our tool: {len(extracted_urls)}")
            
            if len(extracted_urls) != len(links_with_href):
                print(f"   ⚠️  MISMATCH! Found {len(links_with_href)} links but extracted {len(extracted_urls)} URLs")
                
                # Find what's being filtered out
                extracted_hrefs = {url.url for url in extracted_urls}
                all_hrefs = {link.get('href') for link in links_with_href}
                
                filtered_out = all_hrefs - extracted_hrefs
                if filtered_out:
                    print(f"   🔍 Links being filtered out:")
                    for href in list(filtered_out)[:10]:  # Show first 10
                        print(f"      - {href}")
            
            # Check for specific patterns that might be missing
            print(f"\n🔍 Checking for specific patterns...")
            
            # Look for confluence-specific links
            confluence_patterns = [
                r'/wiki/spaces/',
                r'/pages/',
                r'/display/',
                r'pageId=',
                r'spaceKey='
            ]
            
            for pattern in confluence_patterns:
                matches = re.findall(pattern, html_content, re.IGNORECASE)
                print(f"   Pattern '{pattern}': {len(matches)} matches")
            
            # Look for external domains
            external_domains = re.findall(r'https?://([^/\s"\']+)', html_content)
            unique_domains = set(external_domains)
            print(f"   Unique external domains found: {len(unique_domains)}")
            for domain in sorted(unique_domains)[:10]:  # Show first 10
                print(f"      - {domain}")
            
            print(f"\n✅ Analysis complete!")
            print(f"📁 Check debug_page_content.html for full HTML content")
            
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    analyze_html_content()
