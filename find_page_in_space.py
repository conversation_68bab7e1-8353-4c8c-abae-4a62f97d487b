#!/usr/bin/env python3
"""
Find the specific page in the affin space using v1 API
"""
import requests
from requests.auth import HTTPBasicAuth
import json

def find_page_in_affin_space():
    """Find the Environment Info - Azure page in affin space"""
    
    base_url = "https://sdecloud.atlassian.net"
    username = "imitiyaz"
    api_token = "ATATT3xFfGF0hpe-8j9m0miV0UUVy6JaMzsgbXht2AVRI7mPn9sekwyqo9v8yPpx9Ee3kyj8LWWiC9usd0GBSa3Wb0etrIvmqnvNf5oqRiP379Jf1me5DhggNQ_763UlHA0pGnnE6zXIHVOupY_sytIeX756FPaHoGA_pfmzRbHhi7ZPcswMvTak=6D360167"
    
    auth = HTTPBasicAuth(username, api_token)
    headers = {"Accept": "application/json"}
    
    print("🔍 Searching for 'Environment Info - Azure' page in 'affin' space...")
    print()
    
    # Strategy 1: Search by title in affin space
    print("📋 Strategy 1: Search by title in affin space")
    search_url = f"{base_url}/wiki/rest/api/content/search"
    search_params = {
        "cql": "space=affin AND title~\"Environment Info\"",
        "expand": "body.storage,space,version",
        "limit": 50
    }
    
    try:
        response = requests.get(search_url, auth=auth, headers=headers, params=search_params, timeout=10)
        print(f"   Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            results = data.get('results', [])
            print(f"   Found {len(results)} results")
            
            for result in results:
                page_id = result.get('id', 'N/A')
                title = result.get('title', 'N/A')
                space_key = result.get('space', {}).get('key', 'N/A')
                print(f"      - ID: {page_id}, Title: {title}, Space: {space_key}")
                
                # Check if this is our target page
                if "environment" in title.lower() and "azure" in title.lower():
                    print(f"   🎯 Found target page!")
                    
                    # Check if it has body content
                    if 'body' in result and result['body'] and 'storage' in result['body']:
                        html_content = result['body']['storage']['value']
                        print(f"   📄 Content length: {len(html_content)} chars")
                        
                        # Count links
                        import re
                        links = re.findall(r'<a[^>]*href=["\']([^"\']*)["\'][^>]*>', html_content)
                        print(f"   🔗 Links found: {len(links)}")
                        
                        if links:
                            print(f"   Sample links: {links[:5]}")
                            
                            # Save the content
                            with open('found_page_content.html', 'w', encoding='utf-8') as f:
                                f.write(html_content)
                            print(f"   💾 Saved content to: found_page_content.html")
                            
                            return True, page_id, html_content
        else:
            print(f"   ❌ Search failed: {response.text}")
    
    except Exception as e:
        print(f"   ❌ Exception: {str(e)}")
    
    print()
    
    # Strategy 2: List all pages in affin space
    print("📋 Strategy 2: List all pages in affin space")
    content_url = f"{base_url}/wiki/rest/api/content"
    content_params = {
        "spaceKey": "affin",
        "expand": "body.storage,space,version",
        "limit": 100
    }
    
    try:
        response = requests.get(content_url, auth=auth, headers=headers, params=content_params, timeout=10)
        print(f"   Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            results = data.get('results', [])
            print(f"   Found {len(results)} pages in affin space")
            
            for result in results:
                page_id = result.get('id', 'N/A')
                title = result.get('title', 'N/A')
                page_type = result.get('type', 'N/A')
                print(f"      - ID: {page_id}, Type: {page_type}, Title: {title}")
                
                # Check if this matches our target
                if page_id == "837091329" or ("environment" in title.lower() and "azure" in title.lower()):
                    print(f"   🎯 Found matching page!")
                    
                    # Check content
                    if 'body' in result and result['body'] and 'storage' in result['body']:
                        html_content = result['body']['storage']['value']
                        print(f"   📄 Content length: {len(html_content)} chars")
                        
                        # Count links
                        import re
                        links = re.findall(r'<a[^>]*href=["\']([^"\']*)["\'][^>]*>', html_content)
                        print(f"   🔗 Links found: {len(links)}")
                        
                        if links:
                            print(f"   Sample links: {links[:5]}")
                            
                            # Save the content
                            with open(f'page_{page_id}_content.html', 'w', encoding='utf-8') as f:
                                f.write(html_content)
                            print(f"   💾 Saved content to: page_{page_id}_content.html")
                            
                            return True, page_id, html_content
        else:
            print(f"   ❌ Content listing failed: {response.text}")
    
    except Exception as e:
        print(f"   ❌ Exception: {str(e)}")
    
    print()
    
    # Strategy 3: Search with broader terms
    print("📋 Strategy 3: Search with broader terms")
    broad_search_params = {
        "cql": "space=affin",
        "expand": "body.storage,space,version",
        "limit": 100
    }
    
    try:
        response = requests.get(search_url, auth=auth, headers=headers, params=broad_search_params, timeout=10)
        print(f"   Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            results = data.get('results', [])
            print(f"   Found {len(results)} total results in affin space")
            
            # Look for any page with substantial content
            for result in results:
                page_id = result.get('id', 'N/A')
                title = result.get('title', 'N/A')
                
                if 'body' in result and result['body'] and 'storage' in result['body']:
                    html_content = result['body']['storage']['value']
                    
                    if len(html_content) > 1000:  # Substantial content
                        import re
                        links = re.findall(r'<a[^>]*href=["\']([^"\']*)["\'][^>]*>', html_content)
                        
                        if len(links) > 5:  # More than just navigation links
                            print(f"   📄 Page with content: ID {page_id}, Title: {title}")
                            print(f"      Content: {len(html_content)} chars, Links: {len(links)}")
                            print(f"      Sample links: {links[:3]}")
                            
                            # Save this as a potential match
                            with open(f'potential_page_{page_id}.html', 'w', encoding='utf-8') as f:
                                f.write(html_content)
                            print(f"      💾 Saved to: potential_page_{page_id}.html")
        else:
            print(f"   ❌ Broad search failed: {response.text}")
    
    except Exception as e:
        print(f"   ❌ Exception: {str(e)}")
    
    return False, None, None

if __name__ == "__main__":
    success, page_id, content = find_page_in_affin_space()
    if success:
        print(f"\n🎉 Successfully found the page with content!")
        print(f"   Page ID: {page_id}")
        print(f"   Content length: {len(content)} chars")
    else:
        print(f"\n❌ Could not find the target page with content")
