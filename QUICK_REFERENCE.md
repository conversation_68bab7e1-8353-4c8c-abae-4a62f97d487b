# Confluence URL Extractor - Quick Reference

## 🚀 Quick Start Commands

```bash
# 1. Start the application
python start.py

# 2. Set up configuration
python main.py setup

# 3. Test connection
python main.py test-connection

# 4. Extract URLs
python main.py extract --page-id "*********"

# 5. Stop and cleanup
python stop.py
```

## 📋 Essential Commands

### Setup and Configuration
```bash
python main.py setup                    # Interactive setup
python main.py test-connection          # Test API connection
python main.py version                  # Show version info
python main.py --help                   # Show all commands
```

### URL Extraction
```bash
# Basic extraction
python main.py extract --page-id "*********"

# With custom output directory
python main.py extract --page-id "*********" --output-dir "my_reports"

# Without metadata (faster)
python main.py extract --page-id "*********" --no-metadata

# With debug logging
python main.py extract --page-id "*********" --log-level DEBUG

# Save logs to file
python main.py extract --page-id "*********" --log-file extraction.log
```

### System Management
```bash
python start.py                         # Start with full checks
python start.py --quick-start           # Skip tests, go to setup
python start.py --check-only            # Only check dependencies

python stop.py                          # Graceful stop and cleanup
python stop.py --force                  # Force kill processes
python stop.py --cleanup-only           # Only cleanup files
```

## 🔧 Configuration Options

### Environment Variables (.env file)
```env
# Required
CONFLUENCE_CONFLUENCE_URL=https://company.atlassian.net
CONFLUENCE_USERNAME=<EMAIL>
CONFLUENCE_API_TOKEN=your-api-token

# Optional
CONFLUENCE_PAGE_ID=*********
CONFLUENCE_OUTPUT_DIRECTORY=reports
CONFLUENCE_TIMEOUT=30
CONFLUENCE_MAX_RETRIES=3
CONFLUENCE_RATE_LIMIT_DELAY=0.5
CONFLUENCE_LOG_LEVEL=INFO
CONFLUENCE_LOG_FILE=extractor.log
CONFLUENCE_INCLUDE_METADATA=true
```

### Command Line Options
```bash
--url TEXT                              # Confluence URL
--username TEXT                         # Username/email
--api-token TEXT                        # API token
--page-id TEXT                          # Page ID to extract
--output-dir TEXT                       # Output directory
--include-metadata / --no-metadata      # Include detailed metadata
--deduplicate / --no-deduplicate        # Remove duplicate URLs
--log-level [DEBUG|INFO|WARNING|ERROR]  # Logging level
--log-file PATH                         # Log file path
--config-file PATH                      # Configuration file
```

## 📁 Output Files

### File Naming Convention
```
page_{PAGE_ID}_{TYPE}_{TIMESTAMP}.{EXT}

Examples:
- page_*********_raw_20240123_143022.json
- page_*********_categorized_20240123_143022.csv
- page_*********_summary_20240123_143022.json
```

### File Types
- **raw.json** - All URLs with full metadata
- **raw.csv** - All URLs in CSV format
- **categorized.json** - URLs grouped by domain
- **categorized.csv** - Domain summary with counts
- **summary.json** - Extraction statistics

## 🔍 Finding Page IDs

### From URL
```
https://company.atlassian.net/wiki/spaces/SPACE/pages/*********/Page+Title
                                                      ^^^^^^^^^ 
                                                      Page ID
```

### From Confluence
1. Go to the page in Confluence
2. Click "..." menu → "Page Information"
3. Look for "Page ID" in the details

## 🐛 Troubleshooting

### Common Error Messages
```bash
# Authentication failed
❌ "Authentication failed. Please check your username and API token."
→ Verify credentials, regenerate API token

# Page not found
❌ "Page not found. Please check the page ID."
→ Verify page ID, check permissions

# Connection timeout
❌ "Request timeout after 30 seconds"
→ Check network, increase timeout with --timeout 60

# No URLs found
⚠️ "No URLs found in the page"
→ Check page content, verify permissions
```

### Debug Commands
```bash
# Enable debug logging
python main.py extract --page-id "*********" --log-level DEBUG

# Save debug logs to file
python main.py extract --page-id "*********" --log-level DEBUG --log-file debug.log

# Test connection only
python main.py test-connection

# Check system status
python start.py --check-only
```

## 📊 URL Metadata Fields

### Available Fields
```json
{
  "url": "https://example.com",
  "text": "Link text",
  "title": "Link title attribute",
  "domain": "example.com",
  "link_type": "external|internal|email|telephone",
  "css_class": "CSS classes",
  "element_id": "HTML element ID",
  "target": "_blank|_self",
  "is_external": true,
  "is_confluence_link": false,
  "extracted_at": "2024-01-23T14:30:22"
}
```

### Link Types
- **external** - Links to other websites
- **internal** - Links within same domain
- **email** - mailto: links
- **telephone** - tel: links
- **relative** - Relative URLs

## 🔒 Security Notes

### API Token Security
- Never commit `.env` files to version control
- Regularly rotate API tokens
- Use environment variables in production
- Monitor token usage in Confluence admin

### File Permissions
```bash
# Secure .env file (Unix/Linux)
chmod 600 .env

# Check permissions
ls -la .env
```

## 🧪 Testing

### Run Tests
```bash
# All tests
python -m unittest test_confluence_extractor.py -v

# Specific test class
python -m unittest test_confluence_extractor.TestValidators -v

# Quick dependency check
python start.py --check-only
```

## 📈 Performance Tips

### For Large Pages
```bash
# Increase timeout
--timeout 120

# Disable metadata for speed
--no-metadata

# Use debug logging to monitor progress
--log-level DEBUG
```

### Batch Processing
```python
# Process multiple pages
page_ids = ["123", "456", "789"]
for page_id in page_ids:
    python main.py extract --page-id page_id
```

## 🆘 Getting Help

### Built-in Help
```bash
python main.py --help                   # Main help
python main.py extract --help           # Command-specific help
python start.py --help                  # Start script help
python stop.py --help                   # Stop script help
```

### Documentation Files
- `README.md` - Complete documentation
- `STEP_BY_STEP_GUIDE.md` - Detailed setup guide
- `QUICK_REFERENCE.md` - This file
- `.env.example` - Configuration template

### System Information
```bash
python --version                        # Python version
python main.py version                  # Application version
pip list                                # Installed packages
```

---

## 📋 Checklist for New Users

- [ ] Python 3.8+ installed
- [ ] Run `python start.py`
- [ ] Dependencies installed
- [ ] Configuration completed (`python main.py setup`)
- [ ] Connection tested (`python main.py test-connection`)
- [ ] First extraction successful
- [ ] Output files reviewed
- [ ] Documentation read

**Happy URL extracting! 🎉**
