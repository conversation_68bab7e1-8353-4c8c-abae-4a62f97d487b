#!/usr/bin/env python3
"""
Test with the accessible MOBIUISINTRO space
"""
import requests
from requests.auth import HTTPBasicAuth
import json
import re

def test_accessible_space():
    base_url = 'https://sdecloud.atlassian.net'
    auth = HTTPBasicAuth('imitiyaz', 'ATATT3xFfGF0hpe-8j9m0miV0UUVy6JaMzsgbXht2AVRI7mPn9sekwyqo9v8yPpx9Ee3kyj8LWWiC9usd0GBSa3Wb0etrIvmqnvNf5oqRiP379Jf1me5DhggNQ_763UlHA0pGnnE6zXIHVOupY_sytIeX756FPaHoGA_pfmzRbHhi7ZPcswMvTak=6D360167')
    headers = {'Accept': 'application/json'}

    print("🔍 Testing with accessible MOBIUISINTRO space...")
    
    # Get pages from MOBIUISINTRO space
    print("\n1. Getting pages from MOBIUISINTRO space...")
    content_url = f"{base_url}/wiki/rest/api/content?spaceKey=MOBIUISINTRO&expand=body.storage&limit=10"
    response = requests.get(content_url, auth=auth, headers=headers)
    print(f"   Status: {response.status_code}")
    
    if response.status_code == 200:
        data = response.json()
        pages = data.get('results', [])
        print(f"   Found {len(pages)} pages in 'MOBIUISINTRO' space:")
        
        for page in pages:
            page_id = page.get('id', 'N/A')
            title = page.get('title', 'N/A')
            page_type = page.get('type', 'N/A')
            print(f"      - ID: {page_id}, Type: {page_type}, Title: {title}")
            
            # Test URL extraction on the first page with content
            if 'body' in page and page['body'] and 'storage' in page['body']:
                content = page['body']['storage']['value']
                print(f"        Content length: {len(content)} chars")
                
                # Extract links
                links = re.findall(r'href=["\']([^"\']*)["\']', content)
                print(f"        Links found: {len(links)}")
                
                if links:
                    print(f"        Sample links: {links[:5]}")
                    
                    # Save content for inspection
                    with open(f'page_{page_id}_content.html', 'w', encoding='utf-8') as f:
                        f.write(content)
                    print(f"        Saved content to page_{page_id}_content.html")
                    
                    # Test our URL extractor on this content
                    print(f"        Testing URL extractor...")
                    from url_extractor import URLExtractor
                    extractor = URLExtractor(base_url)
                    extracted_urls = extractor.extract_urls_from_html(content, base_url)
                    print(f"        URLs extracted by our tool: {len(extracted_urls)}")
                    
                    for url in extracted_urls:
                        print(f"          - {url.url} ({url.link_type})")
                    
                    return page_id, content  # Return first page with content for testing
    
    return None, None

if __name__ == "__main__":
    test_accessible_space()
