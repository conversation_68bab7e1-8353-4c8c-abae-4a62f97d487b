"""
Confluence API Client for URL Extractor
"""
import time
import requests
from typing import Dict, Any, Optional
from urllib.parse import urljoin
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry

from logger import get_logger
from config import ConfluenceConfig


class ConfluenceAPIError(Exception):
    """Custom exception for Confluence API errors"""
    pass


class ConfluenceAuthError(ConfluenceAPIError):
    """Authentication-related errors"""
    pass


class ConfluenceClient:
    """
    Production-ready Confluence API client with authentication,
    error handling, rate limiting, and retry logic
    """
    
    def __init__(self, config: ConfluenceConfig):
        """
        Initialize Confluence client
        
        Args:
            config: Configuration object with API credentials and settings
        """
        self.config = config
        self.logger = get_logger()
        self.session = self._create_session()
        self.base_url = config.confluence_url
        
        # Validate configuration
        self._validate_config()
        
    def _validate_config(self) -> None:
        """Validate required configuration parameters"""
        required_fields = ['confluence_url', 'username', 'api_token']
        missing_fields = [field for field in required_fields 
                         if not getattr(self.config, field)]
        
        if missing_fields:
            raise ConfluenceAPIError(
                f"Missing required configuration: {', '.join(missing_fields)}"
            )
    
    def _create_session(self) -> requests.Session:
        """Create requests session with retry strategy and authentication"""
        session = requests.Session()
        
        # Set up authentication
        session.auth = (self.config.username, self.config.api_token)
        
        # Set up retry strategy
        retry_strategy = Retry(
            total=self.config.max_retries,
            backoff_factor=1,
            status_forcelist=[429, 500, 502, 503, 504],
            allowed_methods=["HEAD", "GET", "OPTIONS"]
        )
        
        adapter = HTTPAdapter(max_retries=retry_strategy)
        session.mount("http://", adapter)
        session.mount("https://", adapter)
        
        # Set default headers
        session.headers.update({
            'Accept': 'application/json',
            'Content-Type': 'application/json',
            'User-Agent': 'ConfluenceURLExtractor/1.0'
        })
        
        return session
    
    def _make_request(self, method: str, endpoint: str, **kwargs) -> requests.Response:
        """
        Make HTTP request with error handling and rate limiting
        
        Args:
            method: HTTP method (GET, POST, etc.)
            endpoint: API endpoint
            **kwargs: Additional arguments for requests
            
        Returns:
            Response object
            
        Raises:
            ConfluenceAPIError: For API-related errors
            ConfluenceAuthError: For authentication errors
        """
        # For Atlassian Cloud, determine correct URL format based on endpoint
        if 'atlassian.net' in self.base_url and endpoint.startswith('/rest/api'):
            base_url = self.base_url.rstrip('/')

            # Content API and user API don't use /wiki prefix
            if ('/rest/api/content' in endpoint or
                '/rest/api/user/current' in endpoint):
                url = f"{base_url}{endpoint}"
            else:
                # Other endpoints (like space) use /wiki prefix
                url = f"{base_url}/wiki{endpoint}"
        else:
            # Use urljoin for other cases
            url = urljoin(self.base_url, endpoint)

        # Apply rate limiting
        time.sleep(self.config.rate_limit_delay)

        try:
            self.logger.debug(f"Making {method} request to {url}")
            response = self.session.request(
                method=method,
                url=url,
                timeout=self.config.timeout,
                **kwargs
            )
            
            # Handle authentication errors
            if response.status_code == 401:
                raise ConfluenceAuthError(
                    "Authentication failed. Please check your username and API token."
                )
            
            # Handle other client errors
            if response.status_code == 403:
                raise ConfluenceAPIError(
                    "Access forbidden. Check your permissions for this page."
                )
            
            if response.status_code == 404:
                raise ConfluenceAPIError(
                    "Page not found. Please check the page ID."
                )
            
            # Raise for other HTTP errors
            response.raise_for_status()
            
            self.logger.debug(f"Request successful: {response.status_code}")
            return response
            
        except requests.exceptions.Timeout:
            raise ConfluenceAPIError(
                f"Request timeout after {self.config.timeout} seconds"
            )
        except requests.exceptions.ConnectionError:
            raise ConfluenceAPIError(
                "Connection error. Please check your network and Confluence URL."
            )
        except requests.exceptions.RequestException as e:
            raise ConfluenceAPIError(f"Request failed: {str(e)}")
    
    def test_connection(self) -> bool:
        """
        Test connection to Confluence API

        Returns:
            True if connection successful, False otherwise
        """
        try:
            self.logger.info("Testing Confluence API connection...")

            # Try the user endpoint first (most common)
            try:
                response = self._make_request('GET', '/rest/api/user/current')
                user_data = response.json()
                self.logger.info(f"Connected successfully as: {user_data.get('displayName', 'Unknown')}")
                return True
            except ConfluenceAPIError as e:
                if "404" in str(e) or "not found" in str(e).lower():
                    # Try alternative endpoint for older Confluence versions
                    self.logger.debug("Trying alternative endpoint...")
                    response = self._make_request('GET', '/rest/api/space')
                    self.logger.info("Connected successfully (verified with space endpoint)")
                    return True
                else:
                    raise

        except Exception as e:
            self.logger.error(f"Connection test failed: {str(e)}")
            return False
    
    def get_page_content(self, page_id: str, expand: str = "body.storage") -> Dict[str, Any]:
        """
        Get page content from Confluence
        
        Args:
            page_id: Confluence page ID
            expand: Fields to expand (default: body.storage for HTML content)
            
        Returns:
            Page data dictionary
            
        Raises:
            ConfluenceAPIError: If page retrieval fails
        """
        try:
            self.logger.info(f"Fetching page content for ID: {page_id}")
            endpoint = f"/rest/api/content/{page_id}"
            params = {"expand": expand}
            
            response = self._make_request('GET', endpoint, params=params)
            page_data = response.json()
            
            self.logger.info(f"Successfully retrieved page: {page_data.get('title', 'Unknown')}")
            return page_data
            
        except Exception as e:
            self.logger.error(f"Failed to retrieve page {page_id}: {str(e)}")
            raise
    
    def get_page_html_content(self, page_id: str, space_key: Optional[str] = None, page_title: Optional[str] = None) -> str:
        """
        Get HTML content of a Confluence page

        Args:
            page_id: Confluence page ID
            space_key: Optional space key for direct page access
            page_title: Optional page title for direct page access

        Returns:
            HTML content as string
        """
        # Try REST API first
        try:
            page_data = self.get_page_content(page_id)
            html_content = page_data['body']['storage']['value']
            self.logger.debug(f"Retrieved HTML content via REST API ({len(html_content)} characters)")
            return html_content
        except Exception as api_error:
            self.logger.warning(f"REST API failed: {str(api_error)}")

            # If REST API fails and we have space info, try direct page access
            if space_key:
                return self.get_page_html_content_direct(page_id, space_key, page_title)
            else:
                raise ConfluenceAPIError(
                    "Page content not found or not accessible via REST API. "
                    "Try providing space key for direct page access."
                )

    def get_page_html_content_direct(self, page_id: str, space_key: str, page_title: Optional[str] = None) -> str:
        """
        Get HTML content by fetching the actual Confluence page

        Args:
            page_id: Confluence page ID
            space_key: Space key
            page_title: Optional page title

        Returns:
            HTML content as string
        """
        from url_utils import ConfluenceURLParser

        try:
            # Construct the page URL
            parser = ConfluenceURLParser()
            page_url = parser.construct_confluence_page_url(
                self.base_url, space_key, page_id, page_title
            )

            self.logger.info(f"Fetching page content directly from: {page_url}")

            # Fetch the page HTML
            response = self.session.get(page_url, timeout=self.config.timeout)
            response.raise_for_status()

            self.logger.debug(f"Retrieved page HTML ({len(response.text)} characters)")
            return response.text

        except Exception as e:
            raise ConfluenceAPIError(f"Failed to fetch page content directly: {str(e)}")
    
    def close(self) -> None:
        """Close the session"""
        if self.session:
            self.session.close()
            self.logger.debug("Session closed")
    
    def __enter__(self):
        """Context manager entry"""
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit"""
        self.close()
