#!/usr/bin/env python3
"""
Confluence URL Extractor - Stop Script
=======================================

This script provides a clean way to stop any running instances of the
Confluence URL Extractor and perform cleanup operations.

Usage:
    python stop.py
    python stop.py --force
    python stop.py --cleanup-only
"""

import sys
import os
import signal
import psutil
import shutil
from pathlib import Path
import tempfile

# Color codes for terminal output
class Colors:
    GREEN = '\033[92m'
    RED = '\033[91m'
    YELLOW = '\033[93m'
    BLUE = '\033[94m'
    BOLD = '\033[1m'
    END = '\033[0m'

def print_colored(message, color=Colors.END):
    """Print colored message to terminal"""
    print(f"{color}{message}{Colors.END}")

def print_header():
    """Print application header"""
    print_colored("=" * 60, Colors.BLUE)
    print_colored("🛑 CONFLUENCE URL EXTRACTOR - STOP", Colors.BOLD)
    print_colored("Cleanup and Process Management", Colors.BLUE)
    print_colored("=" * 60, Colors.BLUE)
    print()

def find_running_processes():
    """Find running Confluence URL Extractor processes"""
    print_colored("🔍 Searching for running processes...", Colors.BLUE)
    
    processes = []
    current_pid = os.getpid()
    
    try:
        for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
            try:
                cmdline = proc.info['cmdline']
                if cmdline and any('confluence' in str(arg).lower() for arg in cmdline):
                    if proc.info['pid'] != current_pid:  # Don't include this script
                        processes.append({
                            'pid': proc.info['pid'],
                            'name': proc.info['name'],
                            'cmdline': ' '.join(cmdline)
                        })
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue
    except Exception as e:
        print_colored(f"⚠️  Error searching processes: {str(e)}", Colors.YELLOW)
    
    if processes:
        print_colored(f"Found {len(processes)} running process(es):", Colors.YELLOW)
        for proc in processes:
            print_colored(f"  PID {proc['pid']}: {proc['cmdline'][:80]}...", Colors.YELLOW)
    else:
        print_colored("✅ No running processes found", Colors.GREEN)
    
    return processes

def stop_processes(processes, force=False):
    """Stop running processes"""
    if not processes:
        return True
    
    print_colored("🛑 Stopping processes...", Colors.BLUE)
    
    stopped_count = 0
    for proc_info in processes:
        try:
            proc = psutil.Process(proc_info['pid'])
            
            if force:
                proc.kill()
                print_colored(f"  ⚡ Force killed PID {proc_info['pid']}", Colors.RED)
            else:
                proc.terminate()
                print_colored(f"  🛑 Terminated PID {proc_info['pid']}", Colors.YELLOW)
            
            # Wait for process to stop
            try:
                proc.wait(timeout=5)
                stopped_count += 1
            except psutil.TimeoutExpired:
                if not force:
                    print_colored(f"  ⚠️  PID {proc_info['pid']} didn't stop gracefully, use --force", Colors.YELLOW)
                
        except psutil.NoSuchProcess:
            print_colored(f"  ✅ PID {proc_info['pid']} already stopped", Colors.GREEN)
            stopped_count += 1
        except psutil.AccessDenied:
            print_colored(f"  ❌ Access denied for PID {proc_info['pid']}", Colors.RED)
        except Exception as e:
            print_colored(f"  ❌ Error stopping PID {proc_info['pid']}: {str(e)}", Colors.RED)
    
    if stopped_count == len(processes):
        print_colored("✅ All processes stopped successfully", Colors.GREEN)
        return True
    else:
        print_colored(f"⚠️  Stopped {stopped_count}/{len(processes)} processes", Colors.YELLOW)
        return False

def cleanup_temp_files():
    """Clean up temporary files"""
    print_colored("🧹 Cleaning up temporary files...", Colors.BLUE)
    
    cleaned_count = 0
    
    # Clean up Python cache files
    for root, dirs, files in os.walk('.'):
        # Remove __pycache__ directories
        if '__pycache__' in dirs:
            pycache_path = os.path.join(root, '__pycache__')
            try:
                shutil.rmtree(pycache_path)
                print_colored(f"  🗑️  Removed {pycache_path}", Colors.GREEN)
                cleaned_count += 1
            except Exception as e:
                print_colored(f"  ⚠️  Could not remove {pycache_path}: {str(e)}", Colors.YELLOW)
        
        # Remove .pyc files
        for file in files:
            if file.endswith('.pyc'):
                pyc_path = os.path.join(root, file)
                try:
                    os.remove(pyc_path)
                    print_colored(f"  🗑️  Removed {pyc_path}", Colors.GREEN)
                    cleaned_count += 1
                except Exception as e:
                    print_colored(f"  ⚠️  Could not remove {pyc_path}: {str(e)}", Colors.YELLOW)
    
    # Clean up temporary log files
    temp_patterns = ['*.tmp', '*.temp', '*~', '.DS_Store']
    for pattern in temp_patterns:
        for temp_file in Path('.').glob(pattern):
            try:
                temp_file.unlink()
                print_colored(f"  🗑️  Removed {temp_file}", Colors.GREEN)
                cleaned_count += 1
            except Exception as e:
                print_colored(f"  ⚠️  Could not remove {temp_file}: {str(e)}", Colors.YELLOW)
    
    if cleaned_count > 0:
        print_colored(f"✅ Cleaned up {cleaned_count} temporary files", Colors.GREEN)
    else:
        print_colored("✅ No temporary files to clean", Colors.GREEN)

def cleanup_log_files():
    """Clean up old log files"""
    print_colored("📝 Managing log files...", Colors.BLUE)
    
    log_files = list(Path('.').glob('*.log'))
    if not log_files:
        print_colored("✅ No log files found", Colors.GREEN)
        return
    
    print_colored(f"Found {len(log_files)} log file(s):", Colors.BLUE)
    for log_file in log_files:
        size = log_file.stat().st_size
        size_mb = size / (1024 * 1024)
        print_colored(f"  📄 {log_file.name} ({size_mb:.2f} MB)", Colors.BLUE)
    
    # Ask user what to do with log files
    print_colored("\nWhat would you like to do with log files?", Colors.YELLOW)
    print_colored("  1. Keep all log files", Colors.GREEN)
    print_colored("  2. Archive log files (compress)", Colors.BLUE)
    print_colored("  3. Delete all log files", Colors.RED)
    
    try:
        choice = input("Enter choice (1-3) [1]: ").strip() or "1"
        
        if choice == "2":
            # Archive log files
            import zipfile
            from datetime import datetime
            
            archive_name = f"logs_archive_{datetime.now().strftime('%Y%m%d_%H%M%S')}.zip"
            with zipfile.ZipFile(archive_name, 'w', zipfile.ZIP_DEFLATED) as zipf:
                for log_file in log_files:
                    zipf.write(log_file, log_file.name)
                    log_file.unlink()
            
            print_colored(f"✅ Archived log files to {archive_name}", Colors.GREEN)
            
        elif choice == "3":
            # Delete log files
            for log_file in log_files:
                log_file.unlink()
            print_colored(f"✅ Deleted {len(log_files)} log files", Colors.GREEN)
        else:
            print_colored("✅ Keeping all log files", Colors.GREEN)
            
    except KeyboardInterrupt:
        print_colored("\n⚠️  Log cleanup cancelled", Colors.YELLOW)

def show_cleanup_summary():
    """Show cleanup summary and recommendations"""
    print_colored("\n📊 CLEANUP SUMMARY", Colors.BOLD)
    print_colored("-" * 40, Colors.BLUE)
    
    recommendations = [
        "✅ Processes stopped",
        "✅ Temporary files cleaned",
        "✅ Cache files removed",
        "📝 Log files managed"
    ]
    
    for rec in recommendations:
        print_colored(f"  {rec}", Colors.GREEN)
    
    print()
    print_colored("💡 RECOMMENDATIONS:", Colors.BOLD)
    print_colored("  • Regularly clean up old report files in reports/ directory", Colors.BLUE)
    print_colored("  • Monitor log file sizes if logging to file", Colors.BLUE)
    print_colored("  • Consider archiving old extraction reports", Colors.BLUE)
    
    print()
    print_colored("🔄 To restart the application, run: python start.py", Colors.GREEN)

def main():
    """Main stop script function"""
    import argparse
    
    parser = argparse.ArgumentParser(description='Stop Confluence URL Extractor')
    parser.add_argument('--force', action='store_true',
                       help='Force kill processes instead of graceful termination')
    parser.add_argument('--cleanup-only', action='store_true',
                       help='Only perform cleanup, don\'t stop processes')
    
    args = parser.parse_args()
    
    print_header()
    
    if not args.cleanup_only:
        # Find and stop running processes
        processes = find_running_processes()
        print()
        
        if processes:
            stop_processes(processes, force=args.force)
            print()
    
    # Perform cleanup
    cleanup_temp_files()
    print()
    
    cleanup_log_files()
    print()
    
    # Show summary
    show_cleanup_summary()
    
    print_colored("\n🎉 Confluence URL Extractor stopped and cleaned up successfully!", Colors.GREEN)

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print_colored("\n\n👋 Stop operation cancelled by user", Colors.YELLOW)
        sys.exit(0)
    except Exception as e:
        print_colored(f"\n❌ Stop operation error: {str(e)}", Colors.RED)
        sys.exit(1)
