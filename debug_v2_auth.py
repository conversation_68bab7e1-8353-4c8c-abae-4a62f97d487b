#!/usr/bin/env python3
"""
Debug v2 API authentication issues
"""
import requests
from requests.auth import HTTPBasicAuth
import json

def debug_v2_authentication():
    """Debug why v2 API works in browser but not in Python"""
    
    page_id = "837091329"
    base_url = "https://sdecloud.atlassian.net"
    username = "imitiyaz"
    api_token = "ATATT3xFfGF0hpe-8j9m0miV0UUVy6JaMzsgbXht2AVRI7mPn9sekwyqo9v8yPpx9Ee3kyj8LWWiC9usd0GBSa3Wb0etrIvmqnvNf5oqRiP379Jf1me5DhggNQ_763UlHA0pGnnE6zXIHVOupY_sytIeX756FPaHoGA_pfmzRbHhi7ZPcswMvTak=6D360167"
    
    print("🔍 Debugging v2 API authentication...")
    print(f"   Page ID: {page_id}")
    print(f"   Username: {username}")
    print()
    
    # Test different authentication and header combinations
    test_cases = [
        {
            "name": "Basic Auth + JSON Accept",
            "auth": HTTPBasicAuth(username, api_token),
            "headers": {"Accept": "application/json"}
        },
        {
            "name": "Basic Auth + All Accept",
            "auth": HTT<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(username, api_token),
            "headers": {"Accept": "*/*"}
        },
        {
            "name": "Basic Auth + Browser Headers",
            "auth": HTTPBasicAuth(username, api_token),
            "headers": {
                "Accept": "application/json, text/plain, */*",
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
                "Accept-Language": "en-US,en;q=0.9",
                "Accept-Encoding": "gzip, deflate, br",
                "Connection": "keep-alive",
                "Upgrade-Insecure-Requests": "1"
            }
        },
        {
            "name": "Token in Header",
            "auth": None,
            "headers": {
                "Authorization": f"Bearer {api_token}",
                "Accept": "application/json"
            }
        },
        {
            "name": "Token in Header (Basic format)",
            "auth": None,
            "headers": {
                "Authorization": f"Basic {api_token}",
                "Accept": "application/json"
            }
        }
    ]
    
    # Test the main v2 endpoint
    v2_url = f"{base_url}/wiki/api/v2/pages/{page_id}"
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"🧪 Test {i}: {test_case['name']}")
        print(f"   URL: {v2_url}")
        
        try:
            response = requests.get(
                v2_url,
                auth=test_case['auth'],
                headers=test_case['headers'],
                timeout=10
            )
            
            print(f"   Status: {response.status_code}")
            
            if response.status_code == 200:
                print(f"   ✅ SUCCESS!")
                data = response.json()
                print(f"   Title: {data.get('title', 'N/A')}")
                print(f"   ID: {data.get('id', 'N/A')}")
                
                # Try to get body content
                body_url = f"{v2_url}?body-format=storage"
                print(f"   🔍 Trying body content: {body_url}")
                
                body_response = requests.get(
                    body_url,
                    auth=test_case['auth'],
                    headers=test_case['headers'],
                    timeout=10
                )
                
                print(f"   Body status: {body_response.status_code}")
                if body_response.status_code == 200:
                    body_data = body_response.json()
                    if 'body' in body_data and body_data['body']:
                        print(f"   ✅ Got body content!")
                        
                        # Check for storage format
                        body = body_data['body']
                        if isinstance(body, dict) and 'storage' in body:
                            storage = body['storage']
                            if isinstance(storage, dict) and 'value' in storage:
                                html_content = storage['value']
                                print(f"   📄 HTML content: {len(html_content)} chars")
                                
                                # Quick link count
                                import re
                                links = re.findall(r'<a[^>]*href=["\']([^"\']*)["\'][^>]*>', html_content)
                                print(f"   🔗 Links found: {len(links)}")
                                
                                if len(links) > 2:  # More than just header links
                                    print(f"   🎉 FOUND THE SOLUTION!")
                                    print(f"   Sample links: {links[:5]}")
                                    
                                    # Save the working configuration
                                    working_config = {
                                        "url": body_url,
                                        "auth_method": test_case['name'],
                                        "headers": test_case['headers'],
                                        "use_basic_auth": test_case['auth'] is not None,
                                        "username": username if test_case['auth'] else None,
                                        "links_found": len(links)
                                    }
                                    
                                    with open('working_v2_config.json', 'w') as f:
                                        json.dump(working_config, f, indent=2)
                                    
                                    print(f"   💾 Saved working config to: working_v2_config.json")
                                    return True
                
                break  # Found working auth, no need to test others
                
            elif response.status_code == 401:
                print(f"   ❌ Authentication failed")
            elif response.status_code == 403:
                print(f"   ❌ Access forbidden")
            elif response.status_code == 404:
                print(f"   ❌ Not found")
                try:
                    error_data = response.json()
                    print(f"   Error: {error_data}")
                except:
                    pass
            else:
                print(f"   ❌ Error {response.status_code}: {response.text[:100]}")
                
        except Exception as e:
            print(f"   ❌ Exception: {str(e)}")
        
        print()
    
    print("❌ No working authentication method found")
    
    # Additional debugging: Check what endpoints are accessible
    print("\n🔍 Testing accessible endpoints...")
    auth = HTTPBasicAuth(username, api_token)
    headers = {"Accept": "application/json"}
    
    test_endpoints = [
        "/rest/api/user/current",
        "/wiki/rest/api/space",
        "/wiki/rest/api/content",
        "/wiki/api/v2/spaces",
        "/rest/api/content",
    ]
    
    for endpoint in test_endpoints:
        url = f"{base_url}{endpoint}"
        try:
            response = requests.get(url, auth=auth, headers=headers, timeout=5)
            print(f"   {endpoint}: {response.status_code}")
        except:
            print(f"   {endpoint}: ERROR")
    
    return False

if __name__ == "__main__":
    success = debug_v2_authentication()
    if success:
        print(f"\n🎉 Found working v2 API configuration!")
    else:
        print(f"\n❌ Could not find working v2 API configuration")
