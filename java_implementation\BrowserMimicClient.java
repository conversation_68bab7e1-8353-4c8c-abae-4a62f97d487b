import java.io.*;
import java.net.*;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Advanced Java client that mimics browser behavior to get actual Confluence page content
 */
public class BrowserMimicClient {
    
    private static final String CONFLUENCE_URL = "https://sdecloud.atlassian.net";
    private static final String USERNAME = "imitiyaz";
    private static final String API_TOKEN = "ATATT3xFfGF0hpe-8j9m0miV0UUVy6JaMzsgbXht2AVRI7mPn9sekwyqo9v8yPpx9Ee3kyj8LWWiC9usd0GBSa3Wb0etrIvmqnvNf5oqRiP379Jf1me5DhggNQ_763UlHA0pGnnE6zXIHVOupY_sytIeX756FPaHoGA_pfmzRbHhi7ZPcswMvTak=6D360167";
    private static final String PAGE_URL = "https://sdecloud.atlassian.net/wiki/spaces/affin/pages/837091329/Environment+Info+-+Azure";
    
    private CookieManager cookieManager;
    
    public static void main(String[] args) {
        BrowserMimicClient client = new BrowserMimicClient();
        
        System.out.println("🎭 Browser Mimic Client - Tricking Confluence");
        System.out.println("=" + "=".repeat(50));
        System.out.println("Target URL: " + PAGE_URL);
        System.out.println();
        
        try {
            // Strategy 1: Try session-based authentication
            client.trySessionAuthentication();
            
            // Strategy 2: Try with full browser headers
            client.tryBrowserHeaders();
            
            // Strategy 3: Try multi-step authentication
            client.tryMultiStepAuth();
            
            // Strategy 4: Try alternative content endpoints
            client.tryAlternativeEndpoints();
            
        } catch (Exception e) {
            System.out.println("❌ Error: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    public BrowserMimicClient() {
        // Setup cookie management
        this.cookieManager = new CookieManager();
        CookieHandler.setDefault(cookieManager);
    }
    
    /**
     * Strategy 1: Try to establish a session like a browser would
     */
    public void trySessionAuthentication() {
        System.out.println("🔐 Strategy 1: Session-based Authentication");
        System.out.println("-".repeat(40));
        
        try {
            // Step 1: Get the login page to establish session
            System.out.println("📄 Step 1: Getting login page...");
            String loginUrl = CONFLUENCE_URL + "/login.action";
            HttpURLConnection loginConn = createBrowserConnection(loginUrl);
            
            int loginStatus = loginConn.getResponseCode();
            System.out.println("   Login page status: " + loginStatus);
            
            if (loginStatus == 200) {
                String loginPage = readResponse(loginConn);
                System.out.println("   Login page size: " + loginPage.length() + " chars");
                
                // Look for authentication tokens in the login page
                String atlToken = extractAtlToken(loginPage);
                if (atlToken != null) {
                    System.out.println("   Found ATL token: " + atlToken.substring(0, 10) + "...");
                    
                    // Step 2: Try to authenticate with the token
                    tryTokenAuthentication(atlToken);
                }
            }
            
            // Step 3: Try direct access with session cookies
            System.out.println("📄 Step 3: Trying direct page access with session...");
            tryDirectAccessWithSession();
            
        } catch (Exception e) {
            System.out.println("   ❌ Session auth failed: " + e.getMessage());
        }
        
        System.out.println();
    }
    
    /**
     * Strategy 2: Use comprehensive browser headers
     */
    public void tryBrowserHeaders() {
        System.out.println("🌐 Strategy 2: Full Browser Headers");
        System.out.println("-".repeat(40));
        
        try {
            HttpURLConnection conn = createAdvancedBrowserConnection(PAGE_URL);
            
            int status = conn.getResponseCode();
            System.out.println("   Status: " + status);
            
            if (status == 200) {
                String content = readResponse(conn);
                System.out.println("   Content size: " + content.length() + " chars");
                
                // Check if we got actual content vs redirect page
                if (isActualContent(content)) {
                    System.out.println("   ✅ SUCCESS! Got actual page content");
                    saveAndAnalyzeContent(content, "browser_headers_content.html");
                } else {
                    System.out.println("   ⚠️  Still getting redirect/shell page");
                    
                    // Try to extract and follow any redirects
                    String redirectUrl = extractRedirectUrl(content);
                    if (redirectUrl != null) {
                        System.out.println("   🔄 Found redirect: " + redirectUrl);
                        followRedirect(redirectUrl);
                    }
                }
            }
            
        } catch (Exception e) {
            System.out.println("   ❌ Browser headers failed: " + e.getMessage());
        }
        
        System.out.println();
    }
    
    /**
     * Strategy 3: Multi-step authentication process
     */
    public void tryMultiStepAuth() {
        System.out.println("🔄 Strategy 3: Multi-step Authentication");
        System.out.println("-".repeat(40));
        
        try {
            // Step 1: Access Confluence home to establish context
            System.out.println("   Step 1: Accessing Confluence home...");
            String homeUrl = CONFLUENCE_URL + "/wiki";
            HttpURLConnection homeConn = createAdvancedBrowserConnection(homeUrl);
            int homeStatus = homeConn.getResponseCode();
            System.out.println("   Home status: " + homeStatus);
            
            if (homeStatus == 200) {
                String homeContent = readResponse(homeConn);
                System.out.println("   Home content: " + homeContent.length() + " chars");
                
                // Step 2: Try to access the space first
                System.out.println("   Step 2: Accessing affin space...");
                String spaceUrl = CONFLUENCE_URL + "/wiki/spaces/affin";
                HttpURLConnection spaceConn = createAdvancedBrowserConnection(spaceUrl);
                int spaceStatus = spaceConn.getResponseCode();
                System.out.println("   Space status: " + spaceStatus);
                
                if (spaceStatus == 200) {
                    String spaceContent = readResponse(spaceConn);
                    System.out.println("   Space content: " + spaceContent.length() + " chars");
                    
                    // Step 3: Now try the specific page
                    System.out.println("   Step 3: Accessing target page...");
                    HttpURLConnection pageConn = createAdvancedBrowserConnection(PAGE_URL);
                    int pageStatus = pageConn.getResponseCode();
                    System.out.println("   Page status: " + pageStatus);
                    
                    if (pageStatus == 200) {
                        String pageContent = readResponse(pageConn);
                        System.out.println("   Page content: " + pageContent.length() + " chars");
                        
                        if (isActualContent(pageContent)) {
                            System.out.println("   ✅ SUCCESS! Multi-step auth worked");
                            saveAndAnalyzeContent(pageContent, "multistep_auth_content.html");
                        }
                    }
                }
            }
            
        } catch (Exception e) {
            System.out.println("   ❌ Multi-step auth failed: " + e.getMessage());
        }
        
        System.out.println();
    }
    
    /**
     * Strategy 4: Try alternative content endpoints
     */
    public void tryAlternativeEndpoints() {
        System.out.println("🔍 Strategy 4: Alternative Content Endpoints");
        System.out.println("-".repeat(40));
        
        String[] alternativeUrls = {
            // Export formats
            CONFLUENCE_URL + "/wiki/spaces/affin/pages/837091329?mode=export",
            CONFLUENCE_URL + "/wiki/exportword?pageId=837091329",
            CONFLUENCE_URL + "/wiki/pages/viewpage.action?pageId=837091329",
            
            // Mobile/simplified versions
            CONFLUENCE_URL + "/wiki/mobile/spaces/affin/pages/837091329",
            CONFLUENCE_URL + "/wiki/display/affin/Environment+Info+-+Azure",
            
            // Print versions
            CONFLUENCE_URL + "/wiki/spaces/affin/pages/837091329?print=true",
            
            // Raw content
            CONFLUENCE_URL + "/wiki/rest/api/content/837091329?expand=body.view",
            CONFLUENCE_URL + "/wiki/rest/api/content/837091329?expand=body.export_view",
        };
        
        for (String url : alternativeUrls) {
            tryAlternativeUrl(url);
        }
        
        System.out.println();
    }
    
    /**
     * Create connection with basic browser headers
     */
    private HttpURLConnection createBrowserConnection(String url) throws IOException {
        URL urlObj = new URL(url);
        HttpURLConnection conn = (HttpURLConnection) urlObj.openConnection();
        
        // Basic auth
        String auth = USERNAME + ":" + API_TOKEN;
        String encodedAuth = Base64.getEncoder().encodeToString(auth.getBytes(StandardCharsets.UTF_8));
        conn.setRequestProperty("Authorization", "Basic " + encodedAuth);
        
        // Basic browser headers
        conn.setRequestProperty("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36");
        conn.setRequestProperty("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8");
        conn.setRequestProperty("Accept-Language", "en-US,en;q=0.5");
        conn.setRequestProperty("Accept-Encoding", "gzip, deflate, br");
        conn.setRequestProperty("Connection", "keep-alive");
        conn.setRequestProperty("Upgrade-Insecure-Requests", "1");
        
        conn.setConnectTimeout(15000);
        conn.setReadTimeout(30000);
        
        return conn;
    }
    
    /**
     * Create connection with comprehensive browser headers
     */
    private HttpURLConnection createAdvancedBrowserConnection(String url) throws IOException {
        HttpURLConnection conn = createBrowserConnection(url);
        
        // Additional browser-like headers
        conn.setRequestProperty("Cache-Control", "max-age=0");
        conn.setRequestProperty("sec-ch-ua", "\"Google Chrome\";v=\"91\", \"Chromium\";v=\"91\", \";Not A Brand\";v=\"99\"");
        conn.setRequestProperty("sec-ch-ua-mobile", "?0");
        conn.setRequestProperty("sec-fetch-dest", "document");
        conn.setRequestProperty("sec-fetch-mode", "navigate");
        conn.setRequestProperty("sec-fetch-site", "none");
        conn.setRequestProperty("sec-fetch-user", "?1");
        
        // Confluence-specific headers
        conn.setRequestProperty("X-Atlassian-Token", "no-check");
        conn.setRequestProperty("X-Requested-With", "XMLHttpRequest");
        
        return conn;
    }
    
    /**
     * Extract ATL token from login page
     */
    private String extractAtlToken(String html) {
        Pattern tokenPattern = Pattern.compile("name=\"atl_token\"\\s+value=\"([^\"]+)\"");
        Matcher matcher = tokenPattern.matcher(html);
        return matcher.find() ? matcher.group(1) : null;
    }
    
    /**
     * Try authentication with token
     */
    private void tryTokenAuthentication(String atlToken) {
        System.out.println("   Step 2: Trying token authentication...");
        try {
            String authUrl = CONFLUENCE_URL + "/dologin.action";
            HttpURLConnection authConn = createBrowserConnection(authUrl);
            authConn.setRequestMethod("POST");
            authConn.setDoOutput(true);
            
            // Prepare form data
            String formData = "os_username=" + URLEncoder.encode(USERNAME, "UTF-8") +
                            "&os_password=" + URLEncoder.encode(API_TOKEN, "UTF-8") +
                            "&atl_token=" + URLEncoder.encode(atlToken, "UTF-8") +
                            "&login=Log+in";
            
            authConn.setRequestProperty("Content-Type", "application/x-www-form-urlencoded");
            authConn.setRequestProperty("Content-Length", String.valueOf(formData.length()));
            
            try (OutputStream os = authConn.getOutputStream()) {
                os.write(formData.getBytes(StandardCharsets.UTF_8));
            }
            
            int authStatus = authConn.getResponseCode();
            System.out.println("   Auth status: " + authStatus);
            
            if (authStatus == 302 || authStatus == 200) {
                System.out.println("   ✅ Authentication might have succeeded");
            }
            
        } catch (Exception e) {
            System.out.println("   ⚠️  Token auth error: " + e.getMessage());
        }
    }
    
    /**
     * Try direct access with established session
     */
    private void tryDirectAccessWithSession() {
        try {
            HttpURLConnection conn = createAdvancedBrowserConnection(PAGE_URL);
            int status = conn.getResponseCode();
            System.out.println("   Direct access status: " + status);
            
            if (status == 200) {
                String content = readResponse(conn);
                System.out.println("   Content size: " + content.length() + " chars");
                
                if (isActualContent(content)) {
                    System.out.println("   ✅ SUCCESS! Session-based access worked");
                    saveAndAnalyzeContent(content, "session_based_content.html");
                }
            }
            
        } catch (Exception e) {
            System.out.println("   ⚠️  Direct access error: " + e.getMessage());
        }
    }
    
    /**
     * Check if content is actual page content vs redirect/shell
     */
    private boolean isActualContent(String content) {
        // Look for indicators of actual content vs JavaScript shell
        boolean hasTitle = content.contains("Environment Info") || content.contains("Azure");
        boolean hasLinks = content.contains("<a") && !content.contains("meta http-equiv=\"Refresh\"");
        boolean hasJsonData = content.contains("__INITIAL_STATE__") || content.contains("pageId");
        boolean isNotBinary = !content.contains("�") || content.length() < 1000; // Not binary garbage

        System.out.println("   Content analysis: title=" + hasTitle + ", links=" + hasLinks +
                          ", json=" + hasJsonData + ", notBinary=" + isNotBinary);

        return hasTitle || hasLinks || hasJsonData;
    }
    
    /**
     * Extract redirect URL from content
     */
    private String extractRedirectUrl(String content) {
        Pattern redirectPattern = Pattern.compile("URL=([^\"]+)");
        Matcher matcher = redirectPattern.matcher(content);
        return matcher.find() ? matcher.group(1) : null;
    }
    
    /**
     * Follow redirect URL
     */
    private void followRedirect(String redirectUrl) {
        try {
            System.out.println("   Following redirect to: " + redirectUrl);
            HttpURLConnection conn = createAdvancedBrowserConnection(redirectUrl);
            int status = conn.getResponseCode();
            System.out.println("   Redirect status: " + status);
            
            if (status == 200) {
                String content = readResponse(conn);
                System.out.println("   Redirect content: " + content.length() + " chars");
                
                if (isActualContent(content)) {
                    System.out.println("   ✅ SUCCESS! Redirect worked");
                    saveAndAnalyzeContent(content, "redirect_content.html");
                }
            }
            
        } catch (Exception e) {
            System.out.println("   ⚠️  Redirect error: " + e.getMessage());
        }
    }
    
    /**
     * Try alternative URL
     */
    private void tryAlternativeUrl(String url) {
        try {
            System.out.println("   Trying: " + url);
            HttpURLConnection conn = createAdvancedBrowserConnection(url);
            int status = conn.getResponseCode();
            System.out.printf("   Status: %d", status);
            
            if (status == 200) {
                String content = readResponse(conn);
                System.out.printf(" - Content: %d chars", content.length());
                
                if (isActualContent(content)) {
                    System.out.println(" ✅ SUCCESS!");
                    String filename = "alternative_" + url.hashCode() + ".html";
                    saveAndAnalyzeContent(content, filename);
                } else {
                    System.out.println(" - Shell page");
                }
            } else {
                System.out.println(" - Failed");
            }
            
        } catch (Exception e) {
            System.out.println("   Error: " + e.getMessage());
        }
    }
    
    /**
     * Save and analyze content
     */
    private void saveAndAnalyzeContent(String content, String filename) {
        try {
            // Check if content is binary/corrupted
            if (content.contains("�") && content.length() > 50000) {
                System.out.println("   ⚠️  Content appears to be binary/corrupted, skipping analysis");
                return;
            }

            // Save content
            try (FileWriter writer = new FileWriter(filename, StandardCharsets.UTF_8)) {
                writer.write(content);
            }
            System.out.println("   💾 Saved to: " + filename);

            // Try to extract JSON data that might contain actual content
            extractJsonContent(content, filename);

            // Analyze links
            int linkCount = countLinksInHtml(content);
            System.out.println("   🔗 Links found: " + linkCount);

            if (linkCount > 5) { // More than just navigation
                extractAndSaveLinks(content, filename);
            } else if (linkCount > 0) {
                // Even if few links, save them for analysis
                extractAndSaveLinks(content, filename);
            }

        } catch (Exception e) {
            System.out.println("   ⚠️  Save error: " + e.getMessage());
        }
    }

    /**
     * Extract JSON content that might contain the actual page data
     */
    private void extractJsonContent(String content, String sourceFile) {
        try {
            System.out.println("   🔍 Searching for embedded JSON data...");

            // Look for various JSON patterns that Confluence might use
            String[] jsonPatterns = {
                "window\\.__INITIAL_STATE__\\s*=\\s*({.+?});",
                "window\\.__CONFLUENCE_STATE__\\s*=\\s*({.+?});",
                "AJS\\.Meta\\.set\\([\"']([^\"']+)[\"']\\s*,\\s*({.+?})\\)",
                "data-options=[\"']({.+?})[\"']",
                "\"body\"\\s*:\\s*\\{\\s*\"storage\"\\s*:\\s*\\{\\s*\"value\"\\s*:\\s*\"([^\"]+)\"",
                "pageId[\"']?\\s*:\\s*[\"']?837091329[\"']?",
                "\"content\"\\s*:\\s*\"([^\"]+)\""
            };

            boolean foundJson = false;
            for (int i = 0; i < jsonPatterns.length; i++) {
                Pattern pattern = Pattern.compile(jsonPatterns[i], Pattern.DOTALL | Pattern.CASE_INSENSITIVE);
                Matcher matcher = pattern.matcher(content);

                int matches = 0;
                while (matcher.find() && matches < 3) { // Limit to 3 matches per pattern
                    matches++;
                    foundJson = true;

                    System.out.println("   📄 Found JSON pattern " + (i+1) + ", match " + matches);

                    String jsonData = matcher.group(matcher.groupCount()); // Last group
                    if (jsonData.length() > 100) {
                        // Save JSON data
                        String jsonFile = sourceFile.replace(".html", "_json_" + i + "_" + matches + ".txt");
                        try (FileWriter writer = new FileWriter(jsonFile, StandardCharsets.UTF_8)) {
                            writer.write("JSON Pattern " + (i+1) + " Match " + matches + ":\n");
                            writer.write("=" + "=".repeat(50) + "\n\n");
                            writer.write(jsonData);
                        }
                        System.out.println("   💾 Saved JSON to: " + jsonFile);

                        // Try to extract links from JSON data
                        if (jsonData.contains("href") || jsonData.contains("http")) {
                            extractLinksFromJson(jsonData, jsonFile);
                        }
                    }
                }
            }

            if (!foundJson) {
                System.out.println("   ⚠️  No JSON patterns found");

                // Try to find any script tags with data
                Pattern scriptPattern = Pattern.compile("<script[^>]*>(.*?)</script>", Pattern.DOTALL | Pattern.CASE_INSENSITIVE);
                Matcher scriptMatcher = scriptPattern.matcher(content);

                int scriptCount = 0;
                while (scriptMatcher.find() && scriptCount < 5) {
                    String scriptContent = scriptMatcher.group(1);
                    if (scriptContent.length() > 500 && (scriptContent.contains("pageId") || scriptContent.contains("837091329"))) {
                        scriptCount++;
                        System.out.println("   📜 Found relevant script tag " + scriptCount);

                        String scriptFile = sourceFile.replace(".html", "_script_" + scriptCount + ".js");
                        try (FileWriter writer = new FileWriter(scriptFile, StandardCharsets.UTF_8)) {
                            writer.write("Script Tag " + scriptCount + ":\n");
                            writer.write("=" + "=".repeat(50) + "\n\n");
                            writer.write(scriptContent);
                        }
                        System.out.println("   💾 Saved script to: " + scriptFile);
                    }
                }
            }

        } catch (Exception e) {
            System.out.println("   ⚠️  JSON extraction error: " + e.getMessage());
        }
    }

    /**
     * Extract links from JSON data
     */
    private void extractLinksFromJson(String jsonData, String sourceFile) {
        try {
            // Look for URLs in JSON data
            Pattern urlPattern = Pattern.compile("(https?://[^\"'\\s]+)", Pattern.CASE_INSENSITIVE);
            Matcher matcher = urlPattern.matcher(jsonData);

            Set<String> urls = new HashSet<>();
            while (matcher.find()) {
                urls.add(matcher.group(1));
            }

            if (!urls.isEmpty()) {
                System.out.println("   🔗 Found " + urls.size() + " URLs in JSON data");

                String urlsFile = sourceFile.replace(".txt", "_urls.txt");
                try (FileWriter writer = new FileWriter(urlsFile, StandardCharsets.UTF_8)) {
                    writer.write("URLs extracted from JSON:\n");
                    writer.write("=" + "=".repeat(50) + "\n\n");

                    int count = 0;
                    for (String url : urls) {
                        count++;
                        writer.write(String.format("%3d. %s\n", count, url));
                    }
                }
                System.out.println("   💾 Saved URLs to: " + urlsFile);
            }

        } catch (Exception e) {
            System.out.println("   ⚠️  JSON URL extraction error: " + e.getMessage());
        }
    }
    
    /**
     * Count links in HTML
     */
    private int countLinksInHtml(String html) {
        Pattern linkPattern = Pattern.compile("<a[^>]*href=[\"']([^\"']*)[\"'][^>]*>", Pattern.CASE_INSENSITIVE);
        Matcher matcher = linkPattern.matcher(html);
        int count = 0;
        while (matcher.find()) {
            count++;
        }
        return count;
    }
    
    /**
     * Extract and save links
     */
    private void extractAndSaveLinks(String html, String sourceFile) {
        try {
            Pattern linkPattern = Pattern.compile("<a[^>]*href=[\"']([^\"']*)[\"'][^>]*>(.*?)</a>", 
                Pattern.CASE_INSENSITIVE | Pattern.DOTALL);
            Matcher matcher = linkPattern.matcher(html);
            
            StringBuilder linksList = new StringBuilder();
            linksList.append("Links extracted from: ").append(sourceFile).append("\n");
            linksList.append("=" + "=".repeat(60)).append("\n\n");
            
            int count = 0;
            while (matcher.find() && count < 50) { // Limit to 50 links
                String href = matcher.group(1);
                String text = matcher.group(2).replaceAll("<[^>]+>", "").trim();
                
                if (text.length() > 100) {
                    text = text.substring(0, 100) + "...";
                }
                
                count++;
                linksList.append(String.format("%3d. %s\n", count, href));
                linksList.append(String.format("     Text: %s\n\n", text));
            }
            
            String linksFile = sourceFile.replace(".html", "_links.txt");
            try (FileWriter writer = new FileWriter(linksFile, StandardCharsets.UTF_8)) {
                writer.write(linksList.toString());
            }
            
            System.out.println("   📋 Extracted " + count + " links to: " + linksFile);
            
        } catch (Exception e) {
            System.out.println("   ⚠️  Link extraction error: " + e.getMessage());
        }
    }
    
    /**
     * Read response from connection
     */
    private String readResponse(HttpURLConnection conn) throws IOException {
        InputStream inputStream;
        try {
            inputStream = conn.getInputStream();
        } catch (IOException e) {
            inputStream = conn.getErrorStream();
            if (inputStream == null) throw e;
        }
        
        // Handle gzip encoding
        String encoding = conn.getContentEncoding();
        if ("gzip".equals(encoding)) {
            inputStream = new java.util.zip.GZIPInputStream(inputStream);
        }
        
        try (BufferedReader reader = new BufferedReader(
                new InputStreamReader(inputStream, StandardCharsets.UTF_8))) {
            StringBuilder response = new StringBuilder();
            String line;
            while ((line = reader.readLine()) != null) {
                response.append(line).append("\n");
            }
            return response.toString();
        }
    }
}
