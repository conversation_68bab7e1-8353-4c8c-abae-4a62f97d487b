# Java Confluence Client - POC

This is a simple Java client to test Confluence API access and extract URLs from pages.

## Purpose

This Java implementation helps debug the API access issues we encountered with the Python client by:

1. **Testing Multiple API Endpoints** - Tries various Confluence REST API endpoints
2. **Direct Page Access** - Fetches the actual HTML page content
3. **Link Extraction** - Extracts and counts links from both API responses and HTML
4. **File Output** - Saves all responses to local files for inspection

## Requirements

- Java JDK 8 or higher
- Internet connection
- Valid Confluence credentials

## Quick Start

### Windows
```bash
cd java_implementation
build.bat
```

### Linux/Mac
```bash
cd java_implementation
chmod +x build.sh
./build.sh
```

### Manual Execution
```bash
cd java_implementation
javac ConfluenceClient.java
java ConfluenceClient
```

## What It Tests

### API Endpoints Tested:
1. `/rest/api/user/current` - User info
2. `/wiki/rest/api/space` - Available spaces
3. `/wiki/rest/api/content` - Content listing
4. `/rest/api/content/837091329` - Direct page access (v1)
5. `/wiki/rest/api/content/837091329` - Direct page access (v1 with wiki prefix)
6. `/wiki/rest/api/content/837091329?expand=body.storage` - Page with body content
7. `/wiki/rest/api/content/search?cql=id=837091329` - Search by page ID
8. `/wiki/api/v2/pages/837091329` - v2 API page access
9. `/wiki/api/v2/pages/837091329?body-format=storage` - v2 API with body
10. `/wiki/rest/api/content?spaceKey=affin` - Content in affin space
11. `/wiki/rest/api/content?spaceKey=affin&expand=body.storage` - Affin space with body

### Direct Page Access:
1. `https://sdecloud.atlassian.net/wiki/spaces/affin/pages/837091329`
2. `https://sdecloud.atlassian.net/wiki/spaces/affin/pages/837091329/Environment+Info+-+Azure`

## Output Files

The client generates several files for analysis:

- `response_*.json` - API responses that returned successfully
- `api_content_*.html` - HTML content extracted from API responses
- `direct_page_*.html` - HTML content from direct page access
- `extracted_links_*.txt` - List of all extracted links with metadata

## Configuration

The client is pre-configured with:
- **Confluence URL**: `https://sdecloud.atlassian.net`
- **Username**: `imitiyaz`
- **API Token**: (embedded in code)
- **Page ID**: `837091329`

To modify these values, edit the constants at the top of `ConfluenceClient.java`:

```java
private static final String CONFLUENCE_URL = "https://sdecloud.atlassian.net";
private static final String USERNAME = "imitiyaz";
private static final String API_TOKEN = "your-api-token-here";
private static final String PAGE_ID = "837091329";
```

## Expected Results

### If API Access Works:
- ✅ Some endpoints return 200 status
- ✅ JSON responses are saved to files
- ✅ HTML content with links is extracted
- ✅ Link count > 2 (more than just navigation links)

### If API Access Fails:
- ❌ Most endpoints return 404/403/401
- ❌ Only direct page access works (returns HTML shell)
- ❌ Link count ≤ 2 (only navigation links)

## Troubleshooting

### Compilation Errors
- Ensure Java JDK is installed: `java -version` and `javac -version`
- Check that you're in the `java_implementation` directory

### Runtime Errors
- **Connection timeout**: Check internet connection
- **Authentication failed (401)**: Verify API token is valid
- **Access forbidden (403)**: Check permissions for the affin space
- **Not found (404)**: Page or space may not exist with current credentials

### No Links Found
- Check the generated HTML files manually
- Compare with what you see in the browser
- Look for JavaScript-loaded content vs static HTML

## Comparison with Python Client

This Java client helps identify whether the issue is:

1. **Language-specific** - Different behavior between Python and Java HTTP clients
2. **Authentication method** - Different ways of handling Basic Auth
3. **API endpoint** - Which endpoints actually work
4. **Content format** - Whether we get static HTML or dynamic content

## Next Steps

Based on the results:

1. **If Java finds working API endpoints** → Update Python client to use the same endpoints
2. **If Java gets more links** → Compare the HTML content between Java and Python
3. **If Java fails similarly** → Confirms it's an access permission issue, not a Python issue
4. **If direct page access works** → Consider browser automation approach

## Security Note

The API token is embedded in the source code for this POC. In production:
- Use environment variables
- Use secure credential storage
- Rotate tokens regularly
