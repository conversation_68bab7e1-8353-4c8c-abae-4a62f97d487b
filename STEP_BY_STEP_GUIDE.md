# Confluence URL Extractor - Step-by-Step Execution Guide

## 📋 Table of Contents
1. [Prerequisites](#prerequisites)
2. [Installation](#installation)
3. [Configuration](#configuration)
4. [First Run](#first-run)
5. [Usage Examples](#usage-examples)
6. [Troubleshooting](#troubleshooting)
7. [Advanced Usage](#advanced-usage)
8. [Maintenance](#maintenance)

## 🔧 Prerequisites

### System Requirements
- **Python**: 3.8 or higher
- **Operating System**: Windows, macOS, or Linux
- **Memory**: Minimum 512MB RAM
- **Storage**: 100MB free space

### Confluence Requirements
- **Confluence Access**: Valid Confluence account
- **API Token**: Generated from Atlassian Account Settings
- **Permissions**: Read access to the pages you want to extract URLs from

## 📦 Installation

### Step 1: Download/Clone the Project
```bash
# If you have the files, navigate to the project directory
cd confluence-url-extractor
```

### Step 2: Start the Application
```bash
# Run the start script (recommended)
python start.py

# Or run with quick start (skips tests)
python start.py --quick-start

# Or just check dependencies
python start.py --check-only
```

The start script will:
- ✅ Check Python version compatibility
- 📦 Install missing dependencies automatically
- 🧪 Run tests to verify installation
- 📖 Show usage examples
- 🚀 Guide you through next steps

### Step 3: Manual Installation (if needed)
```bash
# Install dependencies manually
pip install -r requirements.txt

# Verify installation
python -m unittest test_confluence_extractor.py -v
```

## ⚙️ Configuration

### Method 1: Interactive Setup (Recommended)
```bash
python main.py setup
```

This will prompt you for:
- Confluence URL (e.g., `https://company.atlassian.net`)
- Username/Email
- API Token
- Test the connection
- Create a `.env` file automatically

### Method 2: Manual Configuration
Create a `.env` file in the project directory:

```env
# Required Settings
CONFLUENCE_CONFLUENCE_URL=https://your-company.atlassian.net
CONFLUENCE_USERNAME=<EMAIL>
CONFLUENCE_API_TOKEN=your-api-token-here

# Optional Settings
CONFLUENCE_OUTPUT_DIRECTORY=reports
CONFLUENCE_TIMEOUT=30
CONFLUENCE_MAX_RETRIES=3
CONFLUENCE_LOG_LEVEL=INFO
```

### Getting Your API Token
1. Go to [Atlassian Account Settings](https://id.atlassian.com/manage-profile/security/api-tokens)
2. Click **"Create API token"**
3. Give it a label (e.g., "URL Extractor")
4. Copy the generated token
5. **Important**: Save this token securely - you won't see it again!

### Finding Page IDs
Page IDs are in the URL when viewing a Confluence page:
```
https://company.atlassian.net/wiki/spaces/SPACE/pages/*********/Page+Title
                                                      ^^^^^^^^^ 
                                                      Page ID
```

## 🚀 First Run

### Step 1: Test Your Connection
```bash
python main.py test-connection
```

Expected output:
```
✅ Connection successful!
Connected successfully as: Your Name
```

### Step 2: Extract URLs from a Test Page
```bash
python main.py extract --page-id "YOUR_PAGE_ID"
```

Replace `YOUR_PAGE_ID` with an actual page ID from your Confluence.

### Step 3: Check the Results
Look in the `reports/` directory for generated files:
- `page_*********_raw_TIMESTAMP.json` - All URLs with metadata
- `page_*********_raw_TIMESTAMP.csv` - All URLs in CSV format
- `page_*********_categorized_TIMESTAMP.json` - URLs grouped by domain
- `page_*********_categorized_TIMESTAMP.csv` - Domain summary
- `page_*********_summary_TIMESTAMP.json` - Extraction statistics

## 📚 Usage Examples

### Basic Extraction
```bash
# Extract URLs from a page
python main.py extract --page-id "*********"
```

### Advanced Extraction
```bash
# Extract with custom output directory
python main.py extract --page-id "*********" --output-dir "my_reports"

# Extract without metadata (faster)
python main.py extract --page-id "*********" --no-metadata

# Extract with debug logging
python main.py extract --page-id "*********" --log-level DEBUG
```

### Using Environment Variables
```bash
# Set page ID in environment
export CONFLUENCE_PAGE_ID="*********"
python main.py extract

# Or on Windows
set CONFLUENCE_PAGE_ID=*********
python main.py extract
```

### Programmatic Usage
```python
from confluence_url_extractor import create_app_from_env

# Use environment variables
with create_app_from_env() as app:
    results = app.run_complete_extraction("*********")
    if results['success']:
        print(f"Extracted {results['summary']['total_urls']} URLs")
```

## 🔧 Troubleshooting

### Common Issues and Solutions

#### 1. Authentication Failed
**Error**: `Authentication failed. Please check your username and API token.`

**Solutions**:
- Verify your username/email is correct
- Check that your API token is valid and not expired
- Ensure you have access to the Confluence instance
- Try regenerating your API token

#### 2. Page Not Found
**Error**: `Page not found. Please check the page ID.`

**Solutions**:
- Verify the page ID is correct
- Check that the page exists and is accessible
- Ensure you have read permissions for the page
- Try accessing the page in your browser first

#### 3. Connection Timeout
**Error**: `Request timeout after 30 seconds`

**Solutions**:
- Check your internet connection
- Verify the Confluence URL is correct and accessible
- Increase timeout: `--timeout 60`
- Check if there are network restrictions

#### 4. No URLs Found
**Error**: `No URLs found in the page`

**Possible Causes**:
- The page genuinely has no links
- The page content is restricted
- The page uses dynamic content loading

**Solutions**:
- Verify the page has links by viewing it in browser
- Check page permissions
- Try a different page

#### 5. Permission Denied
**Error**: `Access forbidden. Check your permissions for this page.`

**Solutions**:
- Ensure you have read access to the page
- Check if the page is in a restricted space
- Contact your Confluence administrator

### Debug Mode
Enable detailed logging for troubleshooting:
```bash
python main.py extract --page-id "*********" --log-level DEBUG --log-file debug.log
```

### Getting Help
```bash
# Show all available commands
python main.py --help

# Show help for specific command
python main.py extract --help

# Show version information
python main.py version
```

## 🎯 Advanced Usage

### Batch Processing Multiple Pages
Create a script to process multiple pages:

```python
#!/usr/bin/env python3
from confluence_url_extractor import create_app_from_env

page_ids = ["*********", "987654321", "555666777"]

with create_app_from_env() as app:
    for page_id in page_ids:
        print(f"Processing page {page_id}...")
        results = app.run_complete_extraction(page_id)
        
        if results['success']:
            print(f"  ✅ Extracted {results['summary']['total_urls']} URLs")
        else:
            print(f"  ❌ Failed: {results['error']}")
```

### Custom Configuration
```python
from confluence_url_extractor import create_app_from_params

app = create_app_from_params(
    confluence_url="https://company.atlassian.net",
    username="<EMAIL>",
    api_token="your-token",
    output_directory="custom_reports",
    log_level="DEBUG"
)

with app:
    results = app.run_complete_extraction("*********")
```

### Filtering Results
```python
# Filter only external URLs
external_urls = [url for url in results['urls'] if url.is_external]

# Filter by domain
github_urls = [url for url in results['urls'] if 'github.com' in url.domain]

# Filter by link type
confluence_urls = [url for url in results['urls'] if url.is_confluence_link]
```

## 🧹 Maintenance

### Regular Cleanup
```bash
# Stop and clean up the application
python stop.py

# Force stop if needed
python stop.py --force

# Clean up only (don't stop processes)
python stop.py --cleanup-only
```

### Managing Output Files
```bash
# Archive old reports
mkdir archive
mv reports/*.json archive/
mv reports/*.csv archive/

# Compress archives
zip -r reports_archive_$(date +%Y%m%d).zip archive/
```

### Updating Dependencies
```bash
# Update all packages
pip install -r requirements.txt --upgrade

# Check for security updates
pip audit

# Update specific package
pip install requests --upgrade
```

### Log Management
```bash
# View recent logs
tail -f confluence_extractor.log

# Rotate logs
mv confluence_extractor.log confluence_extractor.log.old

# Clean up old logs
find . -name "*.log.old" -mtime +30 -delete
```

## 🔒 Security Best Practices

### 1. Protect Your API Token
- Never commit `.env` files to version control
- Use environment variables in production
- Regularly rotate API tokens
- Monitor token usage in Confluence admin

### 2. File Permissions
```bash
# Secure your .env file (Unix/Linux)
chmod 600 .env

# Check file permissions
ls -la .env
```

### 3. Network Security
- Use HTTPS URLs only
- Consider VPN for sensitive environments
- Monitor API usage logs

## 📊 Performance Tips

### 1. Optimize for Large Pages
```bash
# Increase timeout for large pages
python main.py extract --page-id "*********" --timeout 120

# Disable metadata for faster processing
python main.py extract --page-id "*********" --no-metadata
```

### 2. Batch Processing
- Process multiple pages in sequence
- Use rate limiting to avoid API throttling
- Consider parallel processing for large datasets

### 3. Output Management
- Regularly clean up old report files
- Use compression for archived reports
- Consider database storage for large datasets

## 🆘 Support and Resources

### Documentation
- `README.md` - Main documentation
- `--help` - Command-line help
- Code comments - Inline documentation

### Testing
```bash
# Run all tests
python -m unittest test_confluence_extractor.py -v

# Run specific test
python -m unittest test_confluence_extractor.TestValidators -v
```

### Community
- Check existing issues before reporting new ones
- Provide detailed error messages and logs
- Include system information and Python version

---

## 🎉 Quick Start Checklist

- [ ] Python 3.8+ installed
- [ ] Run `python start.py`
- [ ] Dependencies installed automatically
- [ ] Run `python main.py setup`
- [ ] API token configured
- [ ] Connection tested successfully
- [ ] First URL extraction completed
- [ ] Output files generated in `reports/`

**You're ready to extract URLs from Confluence pages! 🚀**
