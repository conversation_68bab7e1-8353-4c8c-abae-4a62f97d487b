import java.io.*;
import java.net.*;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Session hijacking approach to bypass Confluence authentication
 */
public class SessionHijackExtractor {
    
    private static final String CONFLUENCE_URL = "https://sdecloud.atlassian.net";
    private static final String TARGET_PAGE = "https://sdecloud.atlassian.net/wiki/spaces/affin/pages/837091329/Environment+Info+-+Azure";
    
    // Browser session cookies (you'll need to extract these from your browser)
    private static String SESSION_COOKIES = "";
    
    public static void main(String[] args) {
        SessionHijackExtractor extractor = new SessionHijackExtractor();
        
        System.out.println("🎭 Session Hijack Extractor - Bypassing Authentication");
        System.out.println("=" + "=".repeat(60));
        System.out.println("Target: " + TARGET_PAGE);
        System.out.println();
        
        // Step 1: Get session cookies from user
        extractor.getSessionCookies();
        
        // Step 2: Try different bypass techniques
        extractor.trySessionHijack();
        extractor.tryUserAgentSpoofing();
        extractor.tryReferrerSpoofing();
        extractor.tryAlternativeFormats();
    }
    
    /**
     * Get session cookies from user's browser
     */
    private void getSessionCookies() {
        System.out.println("🍪 Step 1: Session Cookie Extraction");
        System.out.println("-".repeat(40));
        System.out.println("To bypass authentication, we need your browser session cookies.");
        System.out.println();
        System.out.println("Instructions:");
        System.out.println("1. Open the working page in your browser:");
        System.out.println("   " + TARGET_PAGE);
        System.out.println("2. Press F12 to open Developer Tools");
        System.out.println("3. Go to Network tab");
        System.out.println("4. Refresh the page");
        System.out.println("5. Click on the first request");
        System.out.println("6. Copy the 'Cookie' header value");
        System.out.println();
        
        Scanner scanner = new Scanner(System.in);
        System.out.print("Paste your session cookies here (or press Enter to skip): ");
        String cookies = scanner.nextLine().trim();
        
        if (!cookies.isEmpty()) {
            SESSION_COOKIES = cookies;
            System.out.println("✅ Session cookies loaded: " + cookies.substring(0, Math.min(50, cookies.length())) + "...");
        } else {
            System.out.println("⚠️  No cookies provided, will try without session hijacking");
        }
        System.out.println();
    }
    
    /**
     * Try session hijacking with browser cookies
     */
    private void trySessionHijack() {
        System.out.println("🎭 Strategy 1: Session Hijacking");
        System.out.println("-".repeat(40));
        
        try {
            HttpURLConnection conn = createSessionConnection(TARGET_PAGE);
            
            int status = conn.getResponseCode();
            System.out.println("   Status: " + status);
            
            if (status == 200) {
                String content = readResponse(conn);
                System.out.println("   Content size: " + content.length() + " chars");
                
                if (isActualPageContent(content)) {
                    System.out.println("   🎉 SUCCESS! Session hijacking worked!");
                    extractAndSaveUrls(content, "session_hijack_content.html");
                } else {
                    System.out.println("   ⚠️  Still getting login/redirect page");
                    saveDebugContent(content, "session_hijack_debug.html");
                }
            } else if (status == 302 || status == 301) {
                String location = conn.getHeaderField("Location");
                System.out.println("   🔄 Redirect to: " + location);
                followRedirectWithSession(location);
            }
            
        } catch (Exception e) {
            System.out.println("   ❌ Error: " + e.getMessage());
        }
        
        System.out.println();
    }
    
    /**
     * Try different User-Agent spoofing
     */
    private void tryUserAgentSpoofing() {
        System.out.println("🤖 Strategy 2: User-Agent Spoofing");
        System.out.println("-".repeat(40));
        
        String[] userAgents = {
            // Chrome on Windows
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            // Firefox on Windows  
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/121.0",
            // Edge on Windows
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 Edg/120.0.0.0",
            // Mobile Chrome (sometimes bypasses desktop restrictions)
            "Mozilla/5.0 (Linux; Android 10; SM-G973F) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Mobile Safari/537.36",
            // Confluence mobile app user agent
            "ConfluenceAndroidApp/8.0.0"
        };
        
        for (int i = 0; i < userAgents.length; i++) {
            System.out.println("   Testing User-Agent " + (i+1) + "...");
            tryWithUserAgent(userAgents[i], "useragent_" + (i+1));
        }
        
        System.out.println();
    }
    
    /**
     * Try referrer spoofing (pretend we came from Confluence)
     */
    private void tryReferrerSpoofing() {
        System.out.println("🔗 Strategy 3: Referrer Spoofing");
        System.out.println("-".repeat(40));
        
        String[] referrers = {
            CONFLUENCE_URL + "/wiki",
            CONFLUENCE_URL + "/wiki/spaces/affin",
            CONFLUENCE_URL + "/wiki/dashboard.action",
            "https://google.com/search?q=confluence+affin+environment+azure"
        };
        
        for (int i = 0; i < referrers.length; i++) {
            System.out.println("   Testing referrer " + (i+1) + ": " + referrers[i]);
            tryWithReferrer(referrers[i], "referrer_" + (i+1));
        }
        
        System.out.println();
    }
    
    /**
     * Try alternative content formats that might bypass restrictions
     */
    private void tryAlternativeFormats() {
        System.out.println("📄 Strategy 4: Alternative Content Formats");
        System.out.println("-".repeat(40));
        
        String[] alternativeUrls = {
            // Add query parameters that might bypass restrictions
            TARGET_PAGE + "?src=contextnavpagetreemode",
            TARGET_PAGE + "?mode=global",
            TARGET_PAGE + "?decorator=printable",
            TARGET_PAGE + "?theme=print",
            TARGET_PAGE + "?view=content",
            TARGET_PAGE + "?format=view",
            
            // Try different URL formats
            CONFLUENCE_URL + "/wiki/display/affin/Environment+Info+-+Azure",
            CONFLUENCE_URL + "/wiki/pages/viewpage.action?pageId=837091329",
            CONFLUENCE_URL + "/wiki/pages/viewpage.action?spaceKey=affin&title=Environment+Info+-+Azure",
            
            // Try mobile/simplified versions
            CONFLUENCE_URL + "/wiki/mobile/#/spaces/affin/pages/837091329",
            CONFLUENCE_URL + "/wiki/spaces/affin/pages/837091329?mobile=true",
            
            // Try RSS/XML feeds (sometimes less restricted)
            CONFLUENCE_URL + "/wiki/createrssfeed.action?types=page&spaces=affin&title=RSS+Feed&maxResults=10",
            
            // Try search results (might include page content)
            CONFLUENCE_URL + "/wiki/dosearchsite.action?queryString=Environment+Info+Azure+space:affin"
        };
        
        for (String url : alternativeUrls) {
            System.out.println("   Trying: " + url.substring(url.lastIndexOf('/') + 1));
            tryAlternativeUrl(url);
        }
        
        System.out.println();
    }
    
    /**
     * Create connection with session cookies and browser headers
     */
    private HttpURLConnection createSessionConnection(String url) throws IOException {
        URL urlObj = new URL(url);
        HttpURLConnection conn = (HttpURLConnection) urlObj.openConnection();
        
        // Add session cookies if available
        if (!SESSION_COOKIES.isEmpty()) {
            conn.setRequestProperty("Cookie", SESSION_COOKIES);
        }
        
        // Full browser headers
        conn.setRequestProperty("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36");
        conn.setRequestProperty("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8");
        conn.setRequestProperty("Accept-Language", "en-US,en;q=0.9");
        conn.setRequestProperty("Accept-Encoding", "gzip, deflate, br");
        conn.setRequestProperty("Connection", "keep-alive");
        conn.setRequestProperty("Upgrade-Insecure-Requests", "1");
        conn.setRequestProperty("Sec-Fetch-Dest", "document");
        conn.setRequestProperty("Sec-Fetch-Mode", "navigate");
        conn.setRequestProperty("Sec-Fetch-Site", "none");
        conn.setRequestProperty("Sec-Fetch-User", "?1");
        conn.setRequestProperty("Cache-Control", "max-age=0");
        
        // Confluence-specific headers
        conn.setRequestProperty("X-Atlassian-Token", "no-check");
        
        conn.setConnectTimeout(15000);
        conn.setReadTimeout(30000);
        
        return conn;
    }
    
    /**
     * Try with specific user agent
     */
    private void tryWithUserAgent(String userAgent, String prefix) {
        try {
            HttpURLConnection conn = createSessionConnection(TARGET_PAGE);
            conn.setRequestProperty("User-Agent", userAgent);
            
            int status = conn.getResponseCode();
            if (status == 200) {
                String content = readResponse(conn);
                System.out.printf("      Status: %d, Size: %d chars", status, content.length());
                
                if (isActualPageContent(content)) {
                    System.out.println(" 🎉 SUCCESS!");
                    extractAndSaveUrls(content, prefix + "_content.html");
                } else {
                    System.out.println(" - Still blocked");
                }
            } else {
                System.out.printf("      Status: %d%n", status);
            }
            
        } catch (Exception e) {
            System.out.println("      Error: " + e.getMessage());
        }
    }
    
    /**
     * Try with specific referrer
     */
    private void tryWithReferrer(String referrer, String prefix) {
        try {
            HttpURLConnection conn = createSessionConnection(TARGET_PAGE);
            conn.setRequestProperty("Referer", referrer);
            
            int status = conn.getResponseCode();
            if (status == 200) {
                String content = readResponse(conn);
                System.out.printf("      Status: %d, Size: %d chars", status, content.length());
                
                if (isActualPageContent(content)) {
                    System.out.println(" 🎉 SUCCESS!");
                    extractAndSaveUrls(content, prefix + "_content.html");
                } else {
                    System.out.println(" - Still blocked");
                }
            } else {
                System.out.printf("      Status: %d%n", status);
            }
            
        } catch (Exception e) {
            System.out.println("      Error: " + e.getMessage());
        }
    }
    
    /**
     * Try alternative URL
     */
    private void tryAlternativeUrl(String url) {
        try {
            HttpURLConnection conn = createSessionConnection(url);
            int status = conn.getResponseCode();
            
            if (status == 200) {
                String content = readResponse(conn);
                System.out.printf("      Status: %d, Size: %d chars", status, content.length());
                
                if (isActualPageContent(content) || content.contains("Environment Info") || content.contains("Azure")) {
                    System.out.println(" 🎉 SUCCESS!");
                    extractAndSaveUrls(content, "alternative_" + Math.abs(url.hashCode()) + ".html");
                } else {
                    System.out.println(" - No target content");
                }
            } else {
                System.out.printf("      Status: %d%n", status);
            }
            
        } catch (Exception e) {
            System.out.println("      Error: " + e.getMessage());
        }
    }
    
    /**
     * Follow redirect with session
     */
    private void followRedirectWithSession(String location) {
        try {
            System.out.println("   Following redirect with session...");
            HttpURLConnection conn = createSessionConnection(location);
            
            int status = conn.getResponseCode();
            System.out.println("   Redirect status: " + status);
            
            if (status == 200) {
                String content = readResponse(conn);
                if (isActualPageContent(content)) {
                    System.out.println("   🎉 Redirect successful!");
                    extractAndSaveUrls(content, "redirect_success.html");
                }
            }
            
        } catch (Exception e) {
            System.out.println("   Redirect error: " + e.getMessage());
        }
    }
    
    /**
     * Check if content is actual page content
     */
    private boolean isActualPageContent(String content) {
        return content.contains("Environment Info") || 
               content.contains("Azure") ||
               (content.contains("<a") && content.contains("href") && 
                !content.contains("id.atlassian.com") && 
                !content.contains("meta http-equiv=\"Refresh\""));
    }
    
    /**
     * Extract and save URLs from content
     */
    private void extractAndSaveUrls(String content, String filename) {
        try {
            // Save full content
            try (FileWriter writer = new FileWriter(filename, StandardCharsets.UTF_8)) {
                writer.write(content);
            }
            System.out.println("   💾 Saved content to: " + filename);
            
            // Extract URLs
            Pattern linkPattern = Pattern.compile("<a[^>]*href=[\"']([^\"']*)[\"'][^>]*>(.*?)</a>", 
                Pattern.CASE_INSENSITIVE | Pattern.DOTALL);
            Matcher matcher = linkPattern.matcher(content);
            
            Set<String> urls = new HashSet<>();
            StringBuilder linksList = new StringBuilder();
            linksList.append("URLs extracted from: ").append(filename).append("\n");
            linksList.append("=" + "=".repeat(60)).append("\n\n");
            
            int count = 0;
            while (matcher.find()) {
                String href = matcher.group(1);
                String text = matcher.group(2).replaceAll("<[^>]+>", "").trim();
                
                if (!href.startsWith("#") && !href.startsWith("javascript:") && !urls.contains(href)) {
                    urls.add(href);
                    count++;
                    
                    if (text.length() > 100) {
                        text = text.substring(0, 100) + "...";
                    }
                    
                    linksList.append(String.format("%3d. %s\n", count, href));
                    linksList.append(String.format("     Text: %s\n\n", text));
                }
            }
            
            // Save links
            String linksFile = filename.replace(".html", "_links.txt");
            try (FileWriter writer = new FileWriter(linksFile, StandardCharsets.UTF_8)) {
                writer.write(linksList.toString());
            }
            
            System.out.println("   🔗 Extracted " + count + " unique URLs to: " + linksFile);
            
            if (count > 10) {
                System.out.println("   🎉 SUCCESS! Found substantial content with many links!");
            }
            
        } catch (Exception e) {
            System.out.println("   ⚠️  URL extraction error: " + e.getMessage());
        }
    }
    
    /**
     * Save debug content for analysis
     */
    private void saveDebugContent(String content, String filename) {
        try {
            String sample = content.length() > 5000 ? content.substring(0, 5000) : content;
            try (FileWriter writer = new FileWriter(filename, StandardCharsets.UTF_8)) {
                writer.write("Debug content sample:\n");
                writer.write("=" + "=".repeat(50) + "\n\n");
                writer.write(sample);
            }
            System.out.println("   💾 Saved debug content to: " + filename);
        } catch (Exception e) {
            System.out.println("   ⚠️  Debug save error: " + e.getMessage());
        }
    }
    
    /**
     * Read response handling compression
     */
    private String readResponse(HttpURLConnection conn) throws IOException {
        InputStream inputStream = conn.getInputStream();
        
        String encoding = conn.getContentEncoding();
        if ("gzip".equals(encoding)) {
            inputStream = new java.util.zip.GZIPInputStream(inputStream);
        }
        
        try (BufferedReader reader = new BufferedReader(
                new InputStreamReader(inputStream, StandardCharsets.UTF_8))) {
            StringBuilder response = new StringBuilder();
            String line;
            while ((line = reader.readLine()) != null) {
                response.append(line).append("\n");
            }
            return response.toString();
        }
    }
}
