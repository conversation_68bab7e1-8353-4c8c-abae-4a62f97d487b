"""
Configuration management for Confluence URL Extractor
"""
import os
from typing import Optional
from pydantic import field_validator
from pydantic_settings import BaseSettings
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()


class ConfluenceConfig(BaseSettings):
    """Configuration settings for Confluence API connection"""
    
    # Confluence connection settings
    confluence_url: str = ""
    api_token: str = ""
    username: str = ""
    page_id: str = ""
    
    # Output settings
    output_directory: str = "reports"
    include_metadata: bool = True
    
    # API settings
    timeout: int = 30
    max_retries: int = 3
    rate_limit_delay: float = 0.5
    
    # Logging settings
    log_level: str = "INFO"
    log_file: Optional[str] = None
    
    model_config = {
        "env_prefix": "CONFLUENCE_",
        "case_sensitive": False,
        "extra": "ignore"
    }

    @field_validator('confluence_url')
    @classmethod
    def validate_confluence_url(cls, v):
        if v and not v.startswith(('http://', 'https://')):
            raise ValueError('Confluence URL must start with http:// or https://')
        return v.rstrip('/') if v else v

    @field_validator('api_token')
    @classmethod
    def validate_api_token(cls, v):
        if v and len(v) < 10:
            raise ValueError('API token appears to be too short')
        return v

    @field_validator('log_level')
    @classmethod
    def validate_log_level(cls, v):
        valid_levels = ['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL']
        if v.upper() not in valid_levels:
            raise ValueError(f'Log level must be one of: {valid_levels}')
        return v.upper()


def get_config() -> ConfluenceConfig:
    """Get configuration instance"""
    return ConfluenceConfig()


# Default configuration for development
DEFAULT_CONFIG = {
    'output_directory': 'reports',
    'timeout': 30,
    'max_retries': 3,
    'rate_limit_delay': 0.5,
    'log_level': 'INFO',
    'include_metadata': True
}
