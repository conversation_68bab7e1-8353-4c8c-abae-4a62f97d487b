import java.io.*;
import java.net.*;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Extract content by intercepting AJAX calls that load actual page content
 */
public class AjaxContentExtractor {
    
    private static final String CONFLUENCE_URL = "https://sdecloud.atlassian.net";
    private static final String USERNAME = "imitiyaz";
    private static final String API_TOKEN = "ATATT3xFfGF0hpe-8j9m0miV0UUVy6JaMzsgbXht2AVRI7mPn9sekwyqo9v8yPpx9Ee3kyj8LWWiC9usd0GBSa3Wb0etrIvmqnvNf5oqRiP379Jf1f1me5DhggNQ_763UlHA0pGnnE6zXIHVOupY_sytIeX756FPaHoGA_pfmzRbHhi7ZPcswMvTak=6D360167";
    private static final String PAGE_ID = "837091329";
    
    public static void main(String[] args) {
        AjaxContentExtractor extractor = new AjaxContentExtractor();
        
        System.out.println("🔄 AJAX Content Extractor - Getting Real Page Content");
        System.out.println("=" + "=".repeat(60));
        System.out.println("Page ID: " + PAGE_ID);
        System.out.println();
        
        // Try different AJAX endpoints that Confluence uses internally
        extractor.tryConfluenceAjaxEndpoints();
        extractor.tryContentViewEndpoints();
        extractor.tryMacroRenderingEndpoints();
        extractor.trySearchBasedExtraction();
    }
    
    /**
     * Try Confluence's internal AJAX endpoints
     */
    private void tryConfluenceAjaxEndpoints() {
        System.out.println("🔄 Strategy 1: Confluence AJAX Endpoints");
        System.out.println("-".repeat(40));
        
        String[] ajaxEndpoints = {
            // Page content AJAX endpoints
            "/wiki/rest/api/content/" + PAGE_ID + "?expand=body.storage,body.view,body.export_view",
            "/wiki/rest/api/content/" + PAGE_ID + "?expand=body.storage&status=current",
            "/wiki/rest/api/content/" + PAGE_ID + "?expand=body.view&status=current",
            
            // Internal content endpoints
            "/wiki/pages/viewpage.action?pageId=" + PAGE_ID + "&decorator=none",
            "/wiki/pages/viewpage.action?pageId=" + PAGE_ID + "&src=contextnavpagetreemode",
            "/wiki/pages/viewpagesrc.action?pageId=" + PAGE_ID,
            
            // AJAX content loading endpoints
            "/wiki/ajax/render/page/" + PAGE_ID,
            "/wiki/ajax/content/" + PAGE_ID,
            "/wiki/rest/prototype/1/content/" + PAGE_ID + "?expand=body",
            
            // Synchrony (real-time editing) endpoints
            "/wiki/synchrony-proxy/v1/page/" + PAGE_ID,
            "/wiki/rest/synchrony/1.0/content/" + PAGE_ID,
            
            // Content rendering endpoints
            "/wiki/rest/tinymce/1/content/" + PAGE_ID + "/rendered",
            "/wiki/rest/tinymce/1/content/" + PAGE_ID,
        };
        
        for (String endpoint : ajaxEndpoints) {
            tryAjaxEndpoint(endpoint);
        }
        
        System.out.println();
    }
    
    /**
     * Try content view endpoints with different parameters
     */
    private void tryContentViewEndpoints() {
        System.out.println("👁️ Strategy 2: Content View Endpoints");
        System.out.println("-".repeat(40));
        
        String[] viewEndpoints = {
            // Different view modes
            "/wiki/spaces/affin/pages/" + PAGE_ID + "?view=content",
            "/wiki/spaces/affin/pages/" + PAGE_ID + "?mode=view",
            "/wiki/spaces/affin/pages/" + PAGE_ID + "?decorator=printable",
            "/wiki/spaces/affin/pages/" + PAGE_ID + "?theme=documentation",
            
            // Content-only views
            "/wiki/pages/viewpage.action?pageId=" + PAGE_ID + "&view=content",
            "/wiki/pages/viewpage.action?pageId=" + PAGE_ID + "&mode=raw",
            "/wiki/pages/viewpage.action?pageId=" + PAGE_ID + "&output=wiki",
            
            // Export views
            "/wiki/spaces/affin/pages/" + PAGE_ID + "/export/pdf",
            "/wiki/spaces/affin/pages/" + PAGE_ID + "/export/word",
            "/wiki/exportword?pageId=" + PAGE_ID,
            
            // Print views
            "/wiki/spaces/affin/pages/" + PAGE_ID + "?print=content",
            "/wiki/pages/viewpage.action?pageId=" + PAGE_ID + "&print=content",
        };
        
        for (String endpoint : viewEndpoints) {
            tryViewEndpoint(endpoint);
        }
        
        System.out.println();
    }
    
    /**
     * Try macro rendering endpoints (where actual content might be)
     */
    private void tryMacroRenderingEndpoints() {
        System.out.println("🔧 Strategy 3: Macro Rendering Endpoints");
        System.out.println("-".repeat(40));
        
        String[] macroEndpoints = {
            // Macro rendering endpoints
            "/wiki/rest/api/content/" + PAGE_ID + "/child/attachment",
            "/wiki/rest/api/content/" + PAGE_ID + "/child/comment",
            "/wiki/rest/api/content/" + PAGE_ID + "/child/page",
            
            // Content properties (might contain links)
            "/wiki/rest/api/content/" + PAGE_ID + "/property",
            "/wiki/rest/api/content/" + PAGE_ID + "/restriction",
            "/wiki/rest/api/content/" + PAGE_ID + "/version",
            
            // Content descendants
            "/wiki/rest/api/content/" + PAGE_ID + "/descendant",
            "/wiki/rest/api/content/" + PAGE_ID + "/descendant/page",
            
            // Labels and metadata
            "/wiki/rest/api/content/" + PAGE_ID + "/label",
            "/wiki/rest/api/content/" + PAGE_ID + "/metadata",
        };
        
        for (String endpoint : macroEndpoints) {
            tryMacroEndpoint(endpoint);
        }
        
        System.out.println();
    }
    
    /**
     * Try search-based extraction to find content
     */
    private void trySearchBasedExtraction() {
        System.out.println("🔍 Strategy 4: Search-Based Content Extraction");
        System.out.println("-".repeat(40));
        
        String[] searchEndpoints = {
            // Search for the specific page
            "/wiki/rest/api/search?cql=id=" + PAGE_ID + "&expand=content.body.storage",
            "/wiki/rest/api/search?cql=id=" + PAGE_ID + "&expand=content.body.view",
            "/wiki/rest/api/search?cql=id=" + PAGE_ID + "&expand=content.body.export_view",
            
            // Search by title
            "/wiki/rest/api/search?cql=title=\"Environment Info - Azure\"&expand=content.body.storage",
            "/wiki/rest/api/search?cql=space=affin AND title~\"Environment Info\"&expand=content.body.storage",
            
            // Content search
            "/wiki/rest/api/content/search?cql=id=" + PAGE_ID + "&expand=body.storage,body.view",
            "/wiki/rest/api/content/search?cql=space=affin AND id=" + PAGE_ID + "&expand=body.storage",
            
            // Advanced search
            "/wiki/rest/api/search?cql=space=affin AND type=page AND id=" + PAGE_ID,
            "/wiki/rest/api/longtask/search?cql=id=" + PAGE_ID,
        };
        
        for (String endpoint : searchEndpoints) {
            trySearchEndpoint(endpoint);
        }
        
        System.out.println();
    }
    
    /**
     * Try AJAX endpoint
     */
    private void tryAjaxEndpoint(String endpoint) {
        try {
            System.out.println("   🔄 " + endpoint.substring(endpoint.lastIndexOf('/') + 1));
            
            HttpURLConnection conn = createAjaxConnection(CONFLUENCE_URL + endpoint);
            int status = conn.getResponseCode();
            
            if (status == 200) {
                String content = readResponse(conn);
                System.out.printf("      Status: %d, Size: %d chars", status, content.length());
                
                if (hasActualContent(content)) {
                    System.out.println(" 🎉 FOUND CONTENT!");
                    extractContentFromResponse(content, "ajax_" + Math.abs(endpoint.hashCode()));
                } else {
                    System.out.println(" - No content");
                }
            } else {
                System.out.printf("      Status: %d%n", status);
            }
            
        } catch (Exception e) {
            System.out.println("      Error: " + e.getMessage());
        }
    }
    
    /**
     * Try view endpoint
     */
    private void tryViewEndpoint(String endpoint) {
        try {
            System.out.println("   👁️ " + endpoint.substring(endpoint.lastIndexOf('/') + 1));
            
            HttpURLConnection conn = createBrowserConnection(CONFLUENCE_URL + endpoint);
            int status = conn.getResponseCode();
            
            if (status == 200) {
                String content = readResponse(conn);
                System.out.printf("      Status: %d, Size: %d chars", status, content.length());
                
                if (hasActualContent(content)) {
                    System.out.println(" 🎉 FOUND CONTENT!");
                    extractContentFromResponse(content, "view_" + Math.abs(endpoint.hashCode()));
                } else {
                    System.out.println(" - No content");
                }
            } else {
                System.out.printf("      Status: %d%n", status);
            }
            
        } catch (Exception e) {
            System.out.println("      Error: " + e.getMessage());
        }
    }
    
    /**
     * Try macro endpoint
     */
    private void tryMacroEndpoint(String endpoint) {
        try {
            System.out.println("   🔧 " + endpoint.substring(endpoint.lastIndexOf('/') + 1));
            
            HttpURLConnection conn = createApiConnection(CONFLUENCE_URL + endpoint);
            int status = conn.getResponseCode();
            
            if (status == 200) {
                String content = readResponse(conn);
                System.out.printf("      Status: %d, Size: %d chars", status, content.length());
                
                if (hasActualContent(content)) {
                    System.out.println(" 🎉 FOUND CONTENT!");
                    extractContentFromResponse(content, "macro_" + Math.abs(endpoint.hashCode()));
                } else {
                    System.out.println(" - No content");
                }
            } else {
                System.out.printf("      Status: %d%n", status);
            }
            
        } catch (Exception e) {
            System.out.println("      Error: " + e.getMessage());
        }
    }
    
    /**
     * Try search endpoint
     */
    private void trySearchEndpoint(String endpoint) {
        try {
            System.out.println("   🔍 " + endpoint.substring(endpoint.indexOf("cql=") + 4, Math.min(endpoint.length(), endpoint.indexOf("cql=") + 30)));
            
            HttpURLConnection conn = createApiConnection(CONFLUENCE_URL + endpoint);
            int status = conn.getResponseCode();
            
            if (status == 200) {
                String content = readResponse(conn);
                System.out.printf("      Status: %d, Size: %d chars", status, content.length());
                
                if (hasActualContent(content)) {
                    System.out.println(" 🎉 FOUND CONTENT!");
                    extractContentFromResponse(content, "search_" + Math.abs(endpoint.hashCode()));
                } else {
                    System.out.println(" - No content");
                }
            } else {
                System.out.printf("      Status: %d%n", status);
            }
            
        } catch (Exception e) {
            System.out.println("      Error: " + e.getMessage());
        }
    }
    
    /**
     * Create AJAX connection
     */
    private HttpURLConnection createAjaxConnection(String url) throws IOException {
        HttpURLConnection conn = createBaseConnection(url);
        
        // AJAX-specific headers
        conn.setRequestProperty("X-Requested-With", "XMLHttpRequest");
        conn.setRequestProperty("Accept", "application/json, text/javascript, */*; q=0.01");
        conn.setRequestProperty("Content-Type", "application/json");
        
        return conn;
    }
    
    /**
     * Create browser connection
     */
    private HttpURLConnection createBrowserConnection(String url) throws IOException {
        HttpURLConnection conn = createBaseConnection(url);
        
        // Browser-specific headers
        conn.setRequestProperty("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8");
        conn.setRequestProperty("Accept-Encoding", "identity"); // No compression for easier parsing
        
        return conn;
    }
    
    /**
     * Create API connection
     */
    private HttpURLConnection createApiConnection(String url) throws IOException {
        HttpURLConnection conn = createBaseConnection(url);
        
        // API-specific headers
        conn.setRequestProperty("Accept", "application/json");
        
        return conn;
    }
    
    /**
     * Create base connection with authentication
     */
    private HttpURLConnection createBaseConnection(String url) throws IOException {
        URL urlObj = new URL(url);
        HttpURLConnection conn = (HttpURLConnection) urlObj.openConnection();
        
        // Authentication
        String auth = USERNAME + ":" + API_TOKEN;
        String encodedAuth = Base64.getEncoder().encodeToString(auth.getBytes(StandardCharsets.UTF_8));
        conn.setRequestProperty("Authorization", "Basic " + encodedAuth);
        
        // Common headers
        conn.setRequestProperty("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36");
        conn.setRequestProperty("Accept-Language", "en-US,en;q=0.9");
        conn.setRequestProperty("Connection", "keep-alive");
        conn.setRequestProperty("X-Atlassian-Token", "no-check");
        
        conn.setConnectTimeout(15000);
        conn.setReadTimeout(30000);
        
        return conn;
    }
    
    /**
     * Check if response has actual content
     */
    private boolean hasActualContent(String content) {
        // Look for indicators of actual page content
        return content.contains("Environment Info") || 
               content.contains("Azure") ||
               content.contains("\"body\"") ||
               content.contains("\"storage\"") ||
               content.contains("\"value\"") ||
               (content.contains("<a") && content.contains("href") && 
                !content.contains("id.atlassian.com") && 
                content.length() > 1000);
    }
    
    /**
     * Extract content from response
     */
    private void extractContentFromResponse(String content, String prefix) {
        try {
            // Save full response
            String filename = prefix + "_response.json";
            try (FileWriter writer = new FileWriter(filename, StandardCharsets.UTF_8)) {
                writer.write(content);
            }
            System.out.println("      💾 Saved to: " + filename);
            
            // Try to extract HTML content from JSON
            String htmlContent = extractHtmlFromJson(content);
            if (htmlContent != null && htmlContent.length() > 100) {
                String htmlFile = prefix + "_content.html";
                try (FileWriter writer = new FileWriter(htmlFile, StandardCharsets.UTF_8)) {
                    writer.write(htmlContent);
                }
                System.out.println("      📄 Extracted HTML to: " + htmlFile);
                
                // Extract URLs from HTML
                int urlCount = extractUrlsFromHtml(htmlContent, prefix + "_urls.txt");
                System.out.println("      🔗 Found " + urlCount + " URLs");
                
                if (urlCount > 10) {
                    System.out.println("      🎉 SUCCESS! Found substantial content!");
                }
            }
            
        } catch (Exception e) {
            System.out.println("      ⚠️  Extraction error: " + e.getMessage());
        }
    }
    
    /**
     * Extract HTML content from JSON response
     */
    private String extractHtmlFromJson(String jsonContent) {
        // Look for different JSON patterns that might contain HTML
        String[] patterns = {
            "\"value\"\\s*:\\s*\"(.*?)\"",
            "\"content\"\\s*:\\s*\"(.*?)\"",
            "\"body\"\\s*:\\s*\"(.*?)\"",
            "\"html\"\\s*:\\s*\"(.*?)\"",
            "\"rendered\"\\s*:\\s*\"(.*?)\"",
        };
        
        for (String patternStr : patterns) {
            Pattern pattern = Pattern.compile(patternStr, Pattern.DOTALL);
            Matcher matcher = pattern.matcher(jsonContent);
            
            if (matcher.find()) {
                String htmlContent = matcher.group(1);
                // Unescape JSON string
                htmlContent = htmlContent.replace("\\\"", "\"")
                                       .replace("\\n", "\n")
                                       .replace("\\r", "\r")
                                       .replace("\\t", "\t")
                                       .replace("\\\\", "\\");
                
                if (htmlContent.length() > 100 && htmlContent.contains("<")) {
                    return htmlContent;
                }
            }
        }
        
        return null;
    }
    
    /**
     * Extract URLs from HTML content
     */
    private int extractUrlsFromHtml(String htmlContent, String outputFile) {
        try {
            Pattern linkPattern = Pattern.compile("<a[^>]*href=[\"']([^\"']*)[\"'][^>]*>(.*?)</a>", 
                Pattern.CASE_INSENSITIVE | Pattern.DOTALL);
            Matcher matcher = linkPattern.matcher(htmlContent);
            
            Set<String> urls = new HashSet<>();
            StringBuilder linksList = new StringBuilder();
            linksList.append("URLs extracted from HTML content:\n");
            linksList.append("=" + "=".repeat(50) + "\n\n");
            
            while (matcher.find()) {
                String href = matcher.group(1);
                String text = matcher.group(2).replaceAll("<[^>]+>", "").trim();
                
                if (!href.startsWith("#") && !href.startsWith("javascript:") && !urls.contains(href)) {
                    urls.add(href);
                    
                    if (text.length() > 100) {
                        text = text.substring(0, 100) + "...";
                    }
                    
                    linksList.append(String.format("%3d. %s\n", urls.size(), href));
                    linksList.append(String.format("     Text: %s\n\n", text));
                }
            }
            
            // Save URLs
            try (FileWriter writer = new FileWriter(outputFile, StandardCharsets.UTF_8)) {
                writer.write(linksList.toString());
            }
            
            return urls.size();
            
        } catch (Exception e) {
            return 0;
        }
    }
    
    /**
     * Read response handling compression
     */
    private String readResponse(HttpURLConnection conn) throws IOException {
        InputStream inputStream = conn.getInputStream();
        
        String encoding = conn.getContentEncoding();
        if ("gzip".equals(encoding)) {
            inputStream = new java.util.zip.GZIPInputStream(inputStream);
        }
        
        try (BufferedReader reader = new BufferedReader(
                new InputStreamReader(inputStream, StandardCharsets.UTF_8))) {
            StringBuilder response = new StringBuilder();
            String line;
            while ((line = reader.readLine()) != null) {
                response.append(line).append("\n");
            }
            return response.toString();
        }
    }
}
