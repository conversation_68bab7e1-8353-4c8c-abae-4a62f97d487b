@echo off
echo 🔐 Building Password Authentication Extractor...
echo.

REM Check if Java is available
java -version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Java is not installed or not in PATH
    echo Please install Java JDK 8 or higher
    pause
    exit /b 1
)

REM Compile the Java file
echo 📦 Compiling PasswordAuthExtractor.java...
javac PasswordAuthExtractor.java

if %errorlevel% neq 0 (
    echo ❌ Compilation failed
    pause
    exit /b 1
)

echo ✅ Compilation successful!
echo.

REM Run the extractor
echo 🚀 Running Password Authentication Extractor...
echo.
echo ⚠️  You will be prompted for your Confluence username and password
echo.
java PasswordAuthExtractor

echo.
echo 📁 Check the generated files in this directory:
dir *.html *.json *.txt 2>nul

pause
