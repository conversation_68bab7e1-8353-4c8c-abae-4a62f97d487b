import requests
from requests.auth import HTTPBasicAuth
import json
import urllib.parse
from datetime import datetime

# Configuration
CONFLUENCE_URL = "https://sdecloud.atlassian.net"
USERNAME = "<EMAIL>"  # Replace with your email
API_TOKEN = "ATATT3xFfGF0ctOI2eFgHkxeBInkwRcVI1oF-XliI8lJsZcy6cnq_JgmCNLhJIho3J5KQ3c3-G6nFq013TGTP7KTc2Bq_HUK7C1v9_Sf6XPA5Gh1FUq_8IH6O-vb_J93LuulNhu4JFklh7acNIopAtiq0p5KGxLi3A8Tcq1yA82ofj0ZH-j118g=5AF05E80"         # Replace with your API token
PAGE_TITLE = "Environment Info - Aliyun - Affin"
PAGE_ID = "*********"
SPACE_KEY = "DEVOPS"

def verify_credentials():
    """Test if credentials work at all"""
    test_endpoint = f"{CONFLUENCE_URL}/rest/api/space"
    try:
        response = requests.get(
            test_endpoint,
            auth=HTTPBasicAuth(USERNAME, API_TOKEN),
            headers={"Accept": "application/json"},
            timeout=10
        )
        print(f"\nBasic API access test:")
        print(f"Status: {response.status_code}")
        
        if response.status_code == 200:
            print("Success! Your credentials work.")
            print(f"Available spaces: {[s['key'] for s in response.json()['results']]}")
            return True
        elif response.status_code == 401:
            print("Authentication failed. Please verify:")
            print("- Your email/username is correct")
            print("- Your API token is valid (generate a new one if unsure)")
            print("- You're using the API token as the password (not your account password)")
        else:
            print(f"Unexpected response: {response.text}")
        return False
    except Exception as e:
        print(f"Connection error: {str(e)}")
        print("Possible issues:")
        print("- Network connectivity problems")
        print("- Corporate firewall blocking requests")
        print("- Invalid Confluence URL")
        return False

def try_direct_page_access():
    """Try to access the page directly with different methods"""
    print("\nAttempting direct page access methods:")
    
    # Method 1: Direct by ID
    endpoint = f"{CONFLUENCE_URL}/rest/api/content/{PAGE_ID}"
    print(f"\n1. Trying direct ID access: {endpoint}")
    response = requests.get(
        endpoint,
        auth=HTTPBasicAuth(USERNAME, API_TOKEN),
        headers={"Accept": "application/json"}
    )
    print(f"Status: {response.status_code}")
    if response.status_code == 200:
        print("Success! Found page by ID")
        return response.json()
    
    # Method 2: Search by title in all spaces
    encoded_title = urllib.parse.quote(PAGE_TITLE)
    endpoint = f"{CONFLUENCE_URL}/rest/api/content/search?cql=title='{encoded_title}'"
    print(f"\n2. Trying title search across all spaces: {endpoint}")
    response = requests.get(
        endpoint,
        auth=HTTPBasicAuth(USERNAME, API_TOKEN),
        headers={"Accept": "application/json"}
    )
    print(f"Status: {response.status_code}")
    if response.status_code == 200:
        results = response.json().get('results', [])
        if results:
            print(f"Found {len(results)} matching pages")
            return results[0]
    
    # Method 3: Find space first
    print("\n3. Attempting to list all accessible spaces")
    spaces_endpoint = f"{CONFLUENCE_URL}/rest/api/space"
    response = requests.get(
        spaces_endpoint,
        auth=HTTPBasicAuth(USERNAME, API_TOKEN),
        headers={"Accept": "application/json"}
    )
    if response.status_code == 200:
        spaces = response.json().get('results', [])
        print(f"Found {len(spaces)} accessible spaces:")
        for space in spaces:
            print(f"- {space['key']}: {space['name']}")
        
        # Try to find similar space key
        possible_space = next((s for s in spaces if SPACE_KEY.lower() in s['key'].lower()), None)
        if possible_space:
            print(f"\nFound similar space: {possible_space['key']}")
            print(f"Retrying with space key: {possible_space['key']}")
            encoded_title = urllib.parse.quote(PAGE_TITLE)
            endpoint = f"{CONFLUENCE_URL}/rest/api/content?spaceKey={possible_space['key']}&title={encoded_title}"
            response = requests.get(
                endpoint,
                auth=HTTPBasicAuth(USERNAME, API_TOKEN),
                headers={"Accept": "application/json"}
            )
            if response.status_code == 200:
                results = response.json().get('results', [])
                if results:
                    return results[0]
    
    return None

if __name__ == "__main__":
    print(f"Confluence Diagnostic Tool for: {CONFLUENCE_URL}")
    
    if not verify_credentials():
        print("\nCannot proceed without valid credentials.")
        print("Please fix authentication issues first.")
        exit(1)
    
    print("\nProceeding with page access attempts...")
    page_data = try_direct_page_access()
    
    if page_data:
        print("\nSuccessfully retrieved page data!")
        print(f"Page ID: {page_data.get('id')}")
        print(f"Space Key: {page_data.get('space', {}).get('key')}")
        print(f"Title: {page_data.get('title')}")
        
        # Save the raw response for inspection
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"confluence_page_{timestamp}.json"
        with open(filename, 'w') as f:
            json.dump(page_data, f, indent=2)
        print(f"\nFull page data saved to {filename}")
    else:
        print("\nFailed to access the page. Possible reasons:")
        print("- The page ID or title is incorrect")
        print("- The page exists but you don't have permission")
        print("- The space key is incorrect")
        print("- The page has been moved or deleted")
        print("\nNext steps:")
        print("1. Manually verify the page exists and is accessible")
        print("2. Check with your Confluence admin about permissions")
        print("3. Try using the exact space key from the URL")