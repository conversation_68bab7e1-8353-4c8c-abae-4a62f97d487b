#!/usr/bin/env python3
"""
Explore the affin space to see what pages are available
"""
import requests
from requests.auth import HTTPBasicAuth
import json

def explore_space():
    base_url = 'https://sdecloud.atlassian.net'
    auth = HTTPBasicAuth('imitiyaz', 'ATATT3xFfGF0hpe-8j9m0miV0UUVy6JaMzsgbXht2AVRI7mPn9sekwyqo9v8yPpx9Ee3kyj8LWWiC9usd0GBSa3Wb0etrIvmqnvNf5oqRiP379Jf1me5DhggNQ_763UlHA0pGnnE6zXIHVOupY_sytIeX756FPaHoGA_pfmzRbHhi7ZPcswMvTak=6D360167')
    headers = {'Accept': 'application/json'}

    print("🔍 Exploring Confluence spaces and pages...")
    
    # Test 1: List all spaces
    print("\n1. Testing spaces endpoint...")
    spaces_url = f"{base_url}/wiki/rest/api/space"
    response = requests.get(spaces_url, auth=auth, headers=headers)
    print(f"   Status: {response.status_code}")
    
    if response.status_code == 200:
        data = response.json()
        spaces = data.get('results', [])
        print(f"   Found {len(spaces)} spaces:")
        for space in spaces[:10]:  # Show first 10
            print(f"      - {space.get('key', 'N/A')}: {space.get('name', 'N/A')}")
    
    # Test 2: Get pages from affin space
    print("\n2. Testing content in 'affin' space...")
    content_url = f"{base_url}/wiki/rest/api/content?spaceKey=affin&limit=20"
    response = requests.get(content_url, auth=auth, headers=headers)
    print(f"   Status: {response.status_code}")
    
    if response.status_code == 200:
        data = response.json()
        pages = data.get('results', [])
        print(f"   Found {len(pages)} pages in 'affin' space:")
        for page in pages:
            page_id = page.get('id', 'N/A')
            title = page.get('title', 'N/A')
            page_type = page.get('type', 'N/A')
            print(f"      - ID: {page_id}, Type: {page_type}, Title: {title}")
    
    # Test 3: Search for pages with "Environment" in title
    print("\n3. Searching for pages with 'Environment' in title...")
    search_url = f"{base_url}/wiki/rest/api/content/search?cql=space=affin AND title~Environment"
    response = requests.get(search_url, auth=auth, headers=headers)
    print(f"   Status: {response.status_code}")
    
    if response.status_code == 200:
        data = response.json()
        pages = data.get('results', [])
        print(f"   Found {len(pages)} pages with 'Environment' in title:")
        for page in pages:
            page_id = page.get('id', 'N/A')
            title = page.get('title', 'N/A')
            print(f"      - ID: {page_id}, Title: {title}")
    
    # Test 4: Try to access the specific page ID directly
    print(f"\n4. Testing direct access to page ID 837091329...")
    direct_urls = [
        f"{base_url}/rest/api/content/837091329",
        f"{base_url}/wiki/rest/api/content/837091329",
        f"{base_url}/wiki/rest/api/content/837091329?expand=body.storage"
    ]
    
    for url in direct_urls:
        response = requests.get(url, auth=auth, headers=headers)
        print(f"   {url}: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"      Title: {data.get('title', 'N/A')}")
    
    # Test 5: Check if the page exists in a different space
    print(f"\n5. Searching for page ID 837091329 across all spaces...")
    search_url = f"{base_url}/wiki/rest/api/content/search?cql=id=837091329"
    response = requests.get(search_url, auth=auth, headers=headers)
    print(f"   Status: {response.status_code}")
    
    if response.status_code == 200:
        data = response.json()
        pages = data.get('results', [])
        print(f"   Found {len(pages)} pages with ID 837091329:")
        for page in pages:
            space_key = page.get('space', {}).get('key', 'N/A')
            title = page.get('title', 'N/A')
            print(f"      - Space: {space_key}, Title: {title}")

if __name__ == "__main__":
    explore_space()
