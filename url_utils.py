"""
URL utilities for Confluence URL Extractor
"""
import re
from urllib.parse import urlparse, parse_qs
from typing import Optional, Dict, Any

from logger import get_logger


class ConfluenceURLParser:
    """
    Utility class for parsing Confluence URLs and extracting page information
    """
    
    def __init__(self):
        self.logger = get_logger()
        
        # Regex patterns for different Confluence URL formats
        self.patterns = {
            # Standard Confluence Cloud URL: /wiki/spaces/SPACE/pages/123456789/Page+Title
            'cloud_standard': re.compile(
                r'/wiki/spaces/([^/]+)/pages/(\d+)/(.+)$'
            ),
            # Direct page URL: /pages/123456789
            'direct_page': re.compile(
                r'/pages/(\d+)(?:/.*)?$'
            ),
            # Legacy format: /display/SPACE/Page+Title
            'legacy_display': re.compile(
                r'/display/([^/]+)/(.+)$'
            ),
            # View page format: /pages/viewpage.action?pageId=123456789
            'view_page': re.compile(
                r'/pages/viewpage\.action'
            )
        }
    
    def extract_page_id_from_url(self, url: str) -> Optional[str]:
        """
        Extract page ID from various Confluence URL formats
        
        Args:
            url: Confluence page URL
            
        Returns:
            Page ID as string, or None if not found
        """
        try:
            self.logger.debug(f"Extracting page ID from URL: {url}")
            
            # Parse the URL
            parsed = urlparse(url)
            path = parsed.path
            query_params = parse_qs(parsed.query)
            
            # Method 1: Check query parameters for pageId
            if 'pageId' in query_params:
                page_id = query_params['pageId'][0]
                self.logger.debug(f"Found page ID in query params: {page_id}")
                return page_id
            
            # Method 2: Extract from standard cloud URL pattern
            match = self.patterns['cloud_standard'].search(path)
            if match:
                space_key, page_id, page_title = match.groups()
                self.logger.debug(f"Found page ID in cloud standard format: {page_id}")
                return page_id
            
            # Method 3: Extract from direct page URL
            match = self.patterns['direct_page'].search(path)
            if match:
                page_id = match.group(1)
                self.logger.debug(f"Found page ID in direct format: {page_id}")
                return page_id
            
            # Method 4: Look for any sequence of 8+ digits in the path
            digit_match = re.search(r'/(\d{8,})(?:/|$)', path)
            if digit_match:
                page_id = digit_match.group(1)
                self.logger.debug(f"Found page ID from digit pattern: {page_id}")
                return page_id
            
            self.logger.warning(f"Could not extract page ID from URL: {url}")
            return None
            
        except Exception as e:
            self.logger.error(f"Error extracting page ID from URL: {str(e)}")
            return None
    
    def parse_confluence_url(self, url: str) -> Dict[str, Any]:
        """
        Parse Confluence URL and extract all available information

        Args:
            url: Confluence page URL

        Returns:
            Dictionary with parsed information
        """
        try:
            parsed = urlparse(url)
            query_params = parse_qs(parsed.query)
            path = parsed.path

            result = {
                'original_url': url,
                'base_url': f"{parsed.scheme}://{parsed.netloc}",
                'path': path,
                'query_params': query_params,
                'page_id': None,
                'space_key': None,
                'page_title': None,
                'url_type': 'unknown',
                'confluence_page_url': None  # The actual page URL for viewing
            }
            
            # Extract page ID
            result['page_id'] = self.extract_page_id_from_url(url)
            
            # Check for standard cloud format
            match = self.patterns['cloud_standard'].search(path)
            if match:
                result['space_key'] = match.group(1)
                result['page_id'] = match.group(2)
                result['page_title'] = match.group(3).replace('+', ' ')
                result['url_type'] = 'cloud_standard'
                # Construct the confluence page URL
                result['confluence_page_url'] = f"{result['base_url']}/wiki/spaces/{result['space_key']}/pages/{result['page_id']}"
                if result['page_title']:
                    title_encoded = result['page_title'].replace(' ', '+')
                    result['confluence_page_url'] += f"/{title_encoded}"
                return result
            
            # Check for view page format
            if self.patterns['view_page'].search(path):
                result['url_type'] = 'view_page'
                if 'pageId' in query_params:
                    result['page_id'] = query_params['pageId'][0]
                if 'spaceKey' in query_params:
                    result['space_key'] = query_params['spaceKey'][0]
                return result
            
            # Check for legacy display format
            match = self.patterns['legacy_display'].search(path)
            if match:
                result['space_key'] = match.group(1)
                result['page_title'] = match.group(2).replace('+', ' ')
                result['url_type'] = 'legacy_display'
                return result
            
            # Check for direct page format
            match = self.patterns['direct_page'].search(path)
            if match:
                result['page_id'] = match.group(1)
                result['url_type'] = 'direct_page'
                return result
            
            return result
            
        except Exception as e:
            self.logger.error(f"Error parsing Confluence URL: {str(e)}")
            return {
                'original_url': url,
                'error': str(e)
            }
    
    def validate_page_id(self, page_id: str) -> bool:
        """
        Validate if a string looks like a valid Confluence page ID
        
        Args:
            page_id: Page ID to validate
            
        Returns:
            True if valid, False otherwise
        """
        if not page_id:
            return False
        
        # Page IDs are typically 8-10 digit numbers
        if re.match(r'^\d{8,12}$', str(page_id)):
            return True
        
        return False
    
    def suggest_page_id_extraction(self, text: str) -> list:
        """
        Suggest possible page IDs from text (URLs, page IDs, etc.)
        
        Args:
            text: Text that might contain page IDs or URLs
            
        Returns:
            List of suggested page IDs
        """
        suggestions = []
        
        # If it's already a valid page ID
        if self.validate_page_id(text.strip()):
            suggestions.append(text.strip())
        
        # Look for URLs in the text
        url_pattern = re.compile(r'https?://[^\s]+')
        urls = url_pattern.findall(text)
        
        for url in urls:
            page_id = self.extract_page_id_from_url(url)
            if page_id and page_id not in suggestions:
                suggestions.append(page_id)
        
        # Look for standalone page IDs (8+ digits)
        page_id_pattern = re.compile(r'\b(\d{8,12})\b')
        page_ids = page_id_pattern.findall(text)
        
        for page_id in page_ids:
            if page_id not in suggestions:
                suggestions.append(page_id)
        
        return suggestions

    def construct_confluence_page_url(self, base_url: str, space_key: str, page_id: str, page_title: Optional[str] = None) -> str:
        """
        Construct a Confluence page URL from components

        Args:
            base_url: Base Confluence URL (e.g., https://company.atlassian.net)
            space_key: Space key (e.g., "affin")
            page_id: Page ID (e.g., "*********")
            page_title: Optional page title

        Returns:
            Constructed Confluence page URL
        """
        base_url = base_url.rstrip('/')
        url = f"{base_url}/wiki/spaces/{space_key}/pages/{page_id}"

        if page_title:
            # Encode title for URL
            title_encoded = page_title.replace(' ', '+').replace('&', '%26')
            url += f"/{title_encoded}"

        return url


def extract_page_id_from_url(url: str) -> Optional[str]:
    """
    Convenience function to extract page ID from URL
    
    Args:
        url: Confluence page URL
        
    Returns:
        Page ID as string, or None if not found
    """
    parser = ConfluenceURLParser()
    return parser.extract_page_id_from_url(url)


def parse_confluence_url(url: str) -> Dict[str, Any]:
    """
    Convenience function to parse Confluence URL
    
    Args:
        url: Confluence page URL
        
    Returns:
        Dictionary with parsed information
    """
    parser = ConfluenceURLParser()
    return parser.parse_confluence_url(url)
