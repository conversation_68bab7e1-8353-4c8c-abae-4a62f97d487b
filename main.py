#!/usr/bin/env python3
"""
Confluence URL Extractor - Production-ready tool for extracting URLs from Confluence pages
"""
import sys
import click
from pathlib import Path
from typing import Optional

from config import ConfluenceConfig, get_config
from confluence_client import ConfluenceClient, Confluence<PERSON>IError, ConfluenceAuthError
from url_extractor import URLExtractor
from data_exporter import DataExporter
from logger import setup_logger, get_logger


@click.group()
@click.option('--config-file', type=click.Path(exists=True), help='Path to configuration file')
@click.option('--log-level', default='INFO', type=click.Choice(['DEBUG', 'INFO', 'WARNING', 'ERROR']), 
              help='Logging level')
@click.option('--log-file', type=click.Path(), help='Log file path')
@click.pass_context
def cli(ctx, config_file, log_level, log_file):
    """Confluence URL Extractor - Extract URLs from Confluence pages with metadata"""
    ctx.ensure_object(dict)
    
    # Setup logging
    logger = setup_logger(level=log_level, log_file=log_file)
    ctx.obj['logger'] = logger
    
    logger.info("Confluence URL Extractor started")


@cli.command()
@click.option('--url', help='Confluence instance URL')
@click.option('--username', help='Confluence username or email')
@click.option('--api-token', help='Confluence API token')
@click.option('--page-id', help='Confluence page ID to extract URLs from')
@click.option('--page-url', help='Confluence page URL (alternative to page-id)')
@click.option('--space-key', help='Confluence space key (e.g., "affin", "PROJ")')
@click.option('--page-title', help='Page title (optional, for better URL construction)')
@click.option('--output-dir', default='reports', help='Output directory for reports')
@click.option('--include-metadata/--no-metadata', default=True, help='Include detailed metadata in exports')
@click.option('--deduplicate/--no-deduplicate', default=True, help='Remove duplicate URLs')
@click.pass_context
def extract(ctx, url, username, api_token, page_id, page_url, space_key, page_title, output_dir, include_metadata, deduplicate):
    """Extract URLs from a Confluence page"""
    logger = ctx.obj['logger']

    try:
        # Import URL utilities
        from url_utils import ConfluenceURLParser
        url_parser = ConfluenceURLParser()

        # Handle page ID extraction from URL if provided
        # Use command-line parameters or extract from URL
        if page_url and not page_id:
            click.echo(f"🔍 Extracting page information from URL: {page_url}")
            parsed_info = url_parser.parse_confluence_url(page_url)

            if parsed_info.get('page_id'):
                page_id = parsed_info['page_id']
                # Use command-line space_key if provided, otherwise use extracted
                if not space_key:
                    space_key = parsed_info.get('space_key')
                # Use command-line page_title if provided, otherwise use extracted
                if not page_title:
                    page_title = parsed_info.get('page_title')

                click.echo(f"✅ Found page ID: {page_id}")
                if space_key:
                    click.echo(f"✅ Using space: {space_key}")
                if page_title:
                    click.echo(f"✅ Using title: {page_title}")
            else:
                click.echo("❌ Could not extract page ID from URL")
                click.echo(f"URL info: {parsed_info}")
                sys.exit(1)

        # Prompt for missing required parameters
        if not url:
            url = click.prompt('Confluence URL (e.g., https://company.atlassian.net)')
        if not username:
            username = click.prompt('Username/Email')
        if not api_token:
            api_token = click.prompt('API Token', hide_input=True)
        if not page_id:
            page_input = click.prompt('Page ID or Page URL')
            # Try to extract page ID from input
            if page_input.startswith('http'):
                parsed_info = url_parser.parse_confluence_url(page_input)
                if parsed_info.get('page_id'):
                    page_id = parsed_info['page_id']
                    # Extract space and title if not already provided
                    if not space_key:
                        space_key = parsed_info.get('space_key')
                    if not page_title:
                        page_title = parsed_info.get('page_title')
                    click.echo(f"✅ Extracted page ID: {page_id}")
                    if space_key:
                        click.echo(f"✅ Extracted space: {space_key}")
                else:
                    click.echo("❌ Could not extract page ID from URL")
                    sys.exit(1)
            else:
                page_id = page_input

        # Prompt for space key if still missing (needed for fallback access)
        if not space_key:
            space_key = click.prompt('Space key (e.g., "affin", "PROJ") - optional for fallback access',
                                   default='', show_default=False)
            if not space_key.strip():
                space_key = None
            else:
                space_key = space_key.strip()
        # Create configuration
        config = ConfluenceConfig(
            confluence_url=url,
            username=username,
            api_token=api_token,
            page_id=page_id,
            space_key=space_key or "",
            page_title=page_title or "",
            output_directory=output_dir,
            include_metadata=include_metadata
        )
        
        # Initialize components
        logger.info("Initializing Confluence URL extractor...")
        
        with ConfluenceClient(config) as client:
            # Test connection
            if not client.test_connection():
                logger.error("Failed to connect to Confluence. Please check your credentials.")
                sys.exit(1)
            
            # Extract URLs
            extractor = URLExtractor(config.confluence_url)
            exporter = DataExporter(config.output_directory)
            
            logger.info(f"Extracting URLs from page ID: {page_id}")
            
            # Get page content
            html_content = client.get_page_html_content(page_id, space_key, page_title)
            
            # Extract URLs
            urls = extractor.extract_urls_from_html(
                html_content, 
                base_url=config.confluence_url,
                deduplicate=deduplicate
            )
            
            if not urls:
                logger.warning("No URLs found in the page")
                return
            
            # Categorize URLs
            categorized_urls = extractor.categorize_urls(urls)
            
            # Generate summary
            summary = extractor.get_extraction_summary(urls)
            
            # Export data
            logger.info("Exporting extracted data...")
            exports = exporter.export_all_formats(
                urls=urls,
                categorized_urls=categorized_urls,
                summary=summary,
                base_filename=f"page_{page_id}"
            )
            
            # Display results
            click.echo("\n" + "="*60)
            click.echo("EXTRACTION COMPLETE")
            click.echo("="*60)
            click.echo(f"Total URLs extracted: {summary['total_urls']}")
            click.echo(f"External URLs: {summary['external_urls']}")
            click.echo(f"Internal URLs: {summary['internal_urls']}")
            click.echo(f"Unique domains: {summary['unique_domains']}")
            
            click.echo("\nTop domains:")
            for domain, count in list(summary['top_domains'].items())[:5]:
                click.echo(f"  {domain}: {count} URLs")
            
            click.echo("\nExported files:")
            for export_type, file_path in exports.items():
                click.echo(f"  {export_type}: {file_path}")
            
            logger.info("URL extraction completed successfully")
            
    except ConfluenceAuthError as e:
        logger.error(f"Authentication error: {str(e)}")
        click.echo(f"Authentication failed: {str(e)}", err=True)
        sys.exit(1)
    except ConfluenceAPIError as e:
        logger.error(f"Confluence API error: {str(e)}")
        click.echo(f"Confluence API error: {str(e)}", err=True)
        sys.exit(1)
    except Exception as e:
        logger.error(f"Unexpected error: {str(e)}")
        click.echo(f"Unexpected error: {str(e)}", err=True)
        sys.exit(1)


@cli.command()
@click.option('--url', prompt='Confluence URL', help='Confluence instance URL')
@click.option('--username', prompt='Username/Email', help='Confluence username or email')
@click.option('--api-token', prompt='API Token', hide_input=True, help='Confluence API token')
@click.pass_context
def test_connection(ctx, url, username, api_token):
    """Test connection to Confluence API"""
    logger = ctx.obj['logger']
    
    try:
        config = ConfluenceConfig(
            confluence_url=url,
            username=username,
            api_token=api_token
        )
        
        with ConfluenceClient(config) as client:
            if client.test_connection():
                click.echo("✅ Connection successful!")
                logger.info("Connection test passed")
            else:
                click.echo("❌ Connection failed!")
                logger.error("Connection test failed")
                sys.exit(1)
                
    except Exception as e:
        logger.error(f"Connection test error: {str(e)}")
        click.echo(f"❌ Connection test failed: {str(e)}", err=True)
        sys.exit(1)


@cli.command()
@click.pass_context
def version(ctx):
    """Show version information"""
    click.echo("Confluence URL Extractor v1.0.0")
    click.echo("Production-ready tool for extracting URLs from Confluence pages")


@cli.command()
@click.pass_context
def setup(ctx):
    """Interactive setup for configuration"""
    logger = ctx.obj['logger']
    
    click.echo("Confluence URL Extractor Setup")
    click.echo("="*40)
    
    # Collect configuration
    confluence_url = click.prompt('Confluence URL (e.g., https://company.atlassian.net)')
    username = click.prompt('Username/Email')
    api_token = click.prompt('API Token', hide_input=True)
    
    # Test connection
    click.echo("\nTesting connection...")
    try:
        config = ConfluenceConfig(
            confluence_url=confluence_url,
            username=username,
            api_token=api_token
        )
        
        with ConfluenceClient(config) as client:
            if client.test_connection():
                click.echo("✅ Connection successful!")
                
                # Create .env file
                env_content = f"""# Confluence API Configuration
CONFLUENCE_CONFLUENCE_URL={confluence_url}
CONFLUENCE_USERNAME={username}
CONFLUENCE_API_TOKEN={api_token}

# Output Configuration
CONFLUENCE_OUTPUT_DIRECTORY=reports
CONFLUENCE_INCLUDE_METADATA=true

# API Configuration
CONFLUENCE_TIMEOUT=30
CONFLUENCE_MAX_RETRIES=3
CONFLUENCE_RATE_LIMIT_DELAY=0.5

# Logging Configuration
CONFLUENCE_LOG_LEVEL=INFO
"""
                
                with open('.env', 'w') as f:
                    f.write(env_content)
                
                click.echo("✅ Configuration saved to .env file")
                click.echo("\nYou can now use the extractor with:")
                click.echo("  python main.py extract --page-id YOUR_PAGE_ID")
                
            else:
                click.echo("❌ Connection failed!")
                sys.exit(1)
                
    except Exception as e:
        click.echo(f"❌ Setup failed: {str(e)}", err=True)
        sys.exit(1)


if __name__ == '__main__':
    cli()
