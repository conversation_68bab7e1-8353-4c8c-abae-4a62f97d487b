#!/usr/bin/env python3
"""
Confluence URL Extractor - Complete Production-Ready Application

This is the main entry point that combines all modules into a single,
easy-to-use application for extracting URLs from Confluence pages.
"""
import sys
import os
from pathlib import Path
from typing import Optional, Dict, Any

# Add current directory to path for imports
sys.path.insert(0, str(Path(__file__).parent))

from config import ConfluenceConfig
from confluence_client import ConfluenceClient, ConfluenceAPIError, ConfluenceAuthError
from url_extractor import URLExtractor
from data_exporter import DataExporter
from logger import setup_logger
from security import get_secure_credential_manager
from validators import (
    validate_confluence_url, validate_email, validate_api_token, 
    validate_page_id, ValidationError
)


class ConfluenceURLExtractorApp:
    """
    Main application class for Confluence URL extraction
    """
    
    def __init__(self, config: Optional[ConfluenceConfig] = None):
        """
        Initialize the application
        
        Args:
            config: Optional configuration object
        """
        self.config = config
        self.logger = None
        self.client = None
        self.extractor = None
        self.exporter = None
        self.security_manager = get_secure_credential_manager()
    
    def setup_logging(self, log_level: str = "INFO", log_file: Optional[str] = None):
        """Setup application logging"""
        self.logger = setup_logger(
            name="confluence_url_extractor",
            level=log_level,
            log_file=log_file
        )
        self.logger.info("Confluence URL Extractor initialized")
    
    def validate_configuration(self) -> bool:
        """
        Validate the current configuration
        
        Returns:
            True if configuration is valid, False otherwise
        """
        if not self.config:
            self.logger.error("No configuration provided")
            return False
        
        try:
            # Validate required fields
            validate_confluence_url(self.config.confluence_url)
            validate_email(self.config.username)
            validate_api_token(self.config.api_token)
            
            if self.config.page_id:
                validate_page_id(self.config.page_id)
            
            self.logger.info("Configuration validation passed")
            return True
            
        except ValidationError as e:
            self.logger.error(f"Configuration validation failed: {str(e)}")
            return False
    
    def initialize_components(self):
        """Initialize all application components"""
        try:
            self.client = ConfluenceClient(self.config)
            self.extractor = URLExtractor(self.config.confluence_url)
            self.exporter = DataExporter(self.config.output_directory)
            
            self.logger.info("All components initialized successfully")
            
        except Exception as e:
            self.logger.error(f"Failed to initialize components: {str(e)}")
            raise
    
    def test_connection(self) -> bool:
        """
        Test connection to Confluence API
        
        Returns:
            True if connection successful, False otherwise
        """
        try:
            if not self.client:
                self.initialize_components()
            
            return self.client.test_connection()
            
        except Exception as e:
            self.logger.error(f"Connection test failed: {str(e)}")
            return False
    
    def extract_urls_from_page(self, page_id: str, deduplicate: bool = True) -> Dict[str, Any]:
        """
        Extract URLs from a Confluence page
        
        Args:
            page_id: Confluence page ID
            deduplicate: Whether to remove duplicate URLs
            
        Returns:
            Dictionary containing extraction results
        """
        try:
            self.logger.info(f"Starting URL extraction for page ID: {page_id}")
            
            # Validate page ID
            validate_page_id(page_id)
            
            # Get page content
            html_content = self.client.get_page_html_content(page_id)
            
            # Extract URLs
            urls = self.extractor.extract_urls_from_html(
                html_content,
                base_url=self.config.confluence_url,
                deduplicate=deduplicate
            )
            
            if not urls:
                self.logger.warning("No URLs found in the page")
                return {
                    'success': True,
                    'urls': [],
                    'categorized_urls': {},
                    'summary': {'total_urls': 0},
                    'message': 'No URLs found in the page'
                }
            
            # Categorize URLs
            categorized_urls = self.extractor.categorize_urls(urls)
            
            # Generate summary
            summary = self.extractor.get_extraction_summary(urls)
            
            self.logger.info(f"Successfully extracted {len(urls)} URLs")
            
            return {
                'success': True,
                'urls': urls,
                'categorized_urls': categorized_urls,
                'summary': summary,
                'page_id': page_id
            }
            
        except Exception as e:
            self.logger.error(f"URL extraction failed: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'page_id': page_id
            }
    
    def export_results(self, extraction_results: Dict[str, Any], base_filename: Optional[str] = None) -> Dict[str, str]:
        """
        Export extraction results to files
        
        Args:
            extraction_results: Results from extract_urls_from_page
            base_filename: Optional base filename for exports
            
        Returns:
            Dictionary with export file paths
        """
        try:
            if not extraction_results.get('success'):
                raise Exception("Cannot export failed extraction results")
            
            urls = extraction_results['urls']
            categorized_urls = extraction_results['categorized_urls']
            summary = extraction_results['summary']
            page_id = extraction_results.get('page_id', 'unknown')
            
            if not base_filename:
                base_filename = f"confluence_page_{page_id}"
            
            # Export all formats
            exports = self.exporter.export_all_formats(
                urls=urls,
                categorized_urls=categorized_urls,
                summary=summary,
                base_filename=base_filename
            )
            
            self.logger.info(f"Exported results to {len(exports)} files")
            return exports
            
        except Exception as e:
            self.logger.error(f"Export failed: {str(e)}")
            raise
    
    def run_complete_extraction(self, page_id: str, deduplicate: bool = True) -> Dict[str, Any]:
        """
        Run complete extraction process including export
        
        Args:
            page_id: Confluence page ID
            deduplicate: Whether to remove duplicate URLs
            
        Returns:
            Complete results including export file paths
        """
        try:
            # Validate configuration
            if not self.validate_configuration():
                raise Exception("Configuration validation failed")
            
            # Initialize components
            if not self.client:
                self.initialize_components()
            
            # Test connection
            if not self.test_connection():
                raise Exception("Failed to connect to Confluence")
            
            # Extract URLs
            extraction_results = self.extract_urls_from_page(page_id, deduplicate)
            
            if not extraction_results['success']:
                return extraction_results
            
            # Export results
            export_files = self.export_results(extraction_results)
            
            # Add export information to results
            extraction_results['export_files'] = export_files
            
            self.logger.info("Complete extraction process finished successfully")
            return extraction_results
            
        except Exception as e:
            self.logger.error(f"Complete extraction failed: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'page_id': page_id
            }
    
    def cleanup(self):
        """Cleanup resources"""
        if self.client:
            self.client.close()
        self.logger.info("Application cleanup completed")
    
    def __enter__(self):
        """Context manager entry"""
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit"""
        self.cleanup()


def create_app_from_env() -> ConfluenceURLExtractorApp:
    """
    Create application instance from environment variables
    
    Returns:
        Configured application instance
    """
    config = ConfluenceConfig()
    app = ConfluenceURLExtractorApp(config)
    app.setup_logging(config.log_level, config.log_file)
    return app


def create_app_from_params(
    confluence_url: str,
    username: str,
    api_token: str,
    page_id: Optional[str] = None,
    space_key: Optional[str] = None,
    page_title: Optional[str] = None,
    output_directory: str = "reports",
    log_level: str = "INFO"
) -> ConfluenceURLExtractorApp:
    """
    Create application instance from parameters
    
    Args:
        confluence_url: Confluence instance URL
        username: Confluence username/email
        api_token: Confluence API token
        page_id: Optional page ID
        output_directory: Output directory for reports
        log_level: Logging level
        
    Returns:
        Configured application instance
    """
    config = ConfluenceConfig(
        confluence_url=confluence_url,
        username=username,
        api_token=api_token,
        page_id=page_id or "",
        space_key=space_key or "",
        page_title=page_title or "",
        output_directory=output_directory,
        log_level=log_level
    )
    
    app = ConfluenceURLExtractorApp(config)
    app.setup_logging(log_level)
    return app


if __name__ == "__main__":
    # Simple command-line usage
    if len(sys.argv) < 2:
        print("Usage: python confluence_url_extractor.py <page_id>")
        print("Make sure to set environment variables or use main.py for full CLI")
        sys.exit(1)
    
    page_id = sys.argv[1]
    
    try:
        with create_app_from_env() as app:
            results = app.run_complete_extraction(page_id)
            
            if results['success']:
                print(f"✅ Successfully extracted {results['summary']['total_urls']} URLs")
                print(f"📁 Files exported to: {app.config.output_directory}")
            else:
                print(f"❌ Extraction failed: {results['error']}")
                sys.exit(1)
                
    except Exception as e:
        print(f"❌ Application error: {str(e)}")
        sys.exit(1)
