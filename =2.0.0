Defaulting to user installation because normal site-packages is not writeable
Collecting pydantic-settings
  Downloading pydantic_settings-2.10.1-py3-none-any.whl.metadata (3.4 kB)
Requirement already satisfied: pydantic>=2.7.0 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (from pydantic-settings) (2.11.7)
Requirement already satisfied: python-dotenv>=0.21.0 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (from pydantic-settings) (1.1.1)
Requirement already satisfied: typing-inspection>=0.4.0 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (from pydantic-settings) (0.4.1)
Requirement already satisfied: annotated-types>=0.6.0 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (from pydantic>=2.7.0->pydantic-settings) (0.7.0)
Requirement already satisfied: pydantic-core==2.33.2 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (from pydantic>=2.7.0->pydantic-settings) (2.33.2)
Requirement already satisfied: typing-extensions>=4.12.2 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (from pydantic>=2.7.0->pydantic-settings) (4.14.1)
Downloading pydantic_settings-2.10.1-py3-none-any.whl (45 kB)
Installing collected packages: pydantic-settings
Successfully installed pydantic-settings-2.10.1
