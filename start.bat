@echo off
REM Confluence URL Extractor - Windows Start Script
REM ===============================================

echo.
echo ================================================================
echo  🔗 CONFLUENCE URL EXTRACTOR - WINDOWS LAUNCHER
echo  Production-Ready URL Extraction Tool
echo ================================================================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python is not installed or not in PATH
    echo Please install Python 3.8+ from https://python.org
    echo.
    pause
    exit /b 1
)

echo ✅ Python found
echo.

REM Check if start.py exists
if not exist "start.py" (
    echo ❌ start.py not found in current directory
    echo Please make sure you're in the correct project directory
    echo.
    pause
    exit /b 1
)

echo 🚀 Starting Confluence URL Extractor...
echo.

REM Run the Python start script
python start.py

echo.
echo 📝 To extract URLs, use:
echo    python main.py extract --page-id YOUR_PAGE_ID
echo.
echo 🛑 To stop and cleanup, use:
echo    python stop.py
echo.
echo 📚 For help, use:
echo    python main.py --help
echo.

pause
