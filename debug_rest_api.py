#!/usr/bin/env python3
"""
Debug script to test different REST API endpoints
"""
import requests
from requests.auth import HTT<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>

def test_rest_api_endpoints():
    """Test different REST API endpoint formats"""
    
    CONFLUENCE_URL = "https://sdecloud.atlassian.net"
    USERNAME = input("Enter username: ").strip()
    API_TOKEN = input("Enter API token: ").strip()
    PAGE_ID = "837091329"
    
    print(f"🔍 Testing REST API endpoints for page ID: {PAGE_ID}")
    print(f"   Base URL: {CONFLUENCE_URL}")
    print()
    
    # Test different endpoint variations
    endpoints_to_test = [
        # Standard REST API endpoints
        f"/rest/api/content/{PAGE_ID}",
        f"/rest/api/content/{PAGE_ID}?expand=body.storage",
        f"/rest/api/content/{PAGE_ID}?expand=body.view",
        f"/rest/api/content/{PAGE_ID}?expand=body.storage,space,version",
        
        # With /wiki prefix
        f"/wiki/rest/api/content/{PAGE_ID}",
        f"/wiki/rest/api/content/{PAGE_ID}?expand=body.storage",
        
        # Alternative formats
        f"/rest/api/content?spaceKey=affin&title=Environment+Info+-+Azure",
        f"/wiki/rest/api/content?spaceKey=affin&title=Environment+Info+-+Azure",
        
        # Search endpoints
        f"/rest/api/content/search?cql=id={PAGE_ID}",
        f"/wiki/rest/api/content/search?cql=id={PAGE_ID}",
        
        # Legacy endpoints
        f"/rest/prototype/1/content/{PAGE_ID}",
        f"/rpc/json-rpc/confluenceservice-v2/getPage?pageId={PAGE_ID}",
    ]
    
    auth = HTTPBasicAuth(USERNAME, API_TOKEN)
    headers = {"Accept": "application/json"}
    
    for endpoint in endpoints_to_test:
        print(f"🧪 Testing endpoint: {endpoint}")
        url = f"{CONFLUENCE_URL}{endpoint}"
        print(f"   Full URL: {url}")
        
        try:
            response = requests.get(
                url,
                auth=auth,
                headers=headers,
                timeout=10
            )
            
            print(f"   Status: {response.status_code}")
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    print(f"   ✅ SUCCESS!")
                    print(f"   Response type: {type(data)}")
                    
                    if isinstance(data, dict):
                        print(f"   Keys: {list(data.keys())}")
                        if 'title' in data:
                            print(f"   Title: {data['title']}")
                        if 'type' in data:
                            print(f"   Type: {data['type']}")
                        if 'body' in data:
                            body_formats = list(data['body'].keys()) if data['body'] else []
                            print(f"   Body formats: {body_formats}")
                            
                            # Check for content
                            for format_name in ['storage', 'view', 'export_view']:
                                if format_name in data['body']:
                                    content = data['body'][format_name].get('value', '')
                                    print(f"   {format_name} content length: {len(content)} chars")
                                    
                                    # Look for links in content
                                    if content:
                                        import re
                                        links = re.findall(r'<a[^>]*href=["\']([^"\']*)["\'][^>]*>', content)
                                        print(f"   Links found in {format_name}: {len(links)}")
                                        if links:
                                            print(f"   Sample links: {links[:3]}")
                    
                    elif isinstance(data, list):
                        print(f"   Array length: {len(data)}")
                        if data:
                            print(f"   First item keys: {list(data[0].keys()) if isinstance(data[0], dict) else 'Not a dict'}")
                    
                    print(f"   🎉 This endpoint works!")
                    return endpoint, data
                    
                except Exception as e:
                    print(f"   ⚠️  JSON parsing error: {str(e)}")
                    print(f"   Raw response: {response.text[:200]}...")
                    
            elif response.status_code == 401:
                print(f"   ❌ Authentication failed")
            elif response.status_code == 403:
                print(f"   ❌ Access forbidden")
            elif response.status_code == 404:
                print(f"   ❌ Not found")
            else:
                print(f"   ❌ Error: {response.text[:200]}")
                
        except Exception as e:
            print(f"   ❌ Exception: {str(e)}")
        
        print()
    
    print("❌ No working endpoint found")
    return None, None

if __name__ == "__main__":
    test_rest_api_endpoints()
