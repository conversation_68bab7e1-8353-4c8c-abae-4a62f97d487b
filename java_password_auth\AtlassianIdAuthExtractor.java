import java.io.*;
import java.net.*;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Confluence URL extractor using Atlassian ID authentication
 * Handles the id.atlassian.com login flow
 */
public class AtlassianIdAuthExtractor {
    
    private static final String CONFLUENCE_URL = "https://sdecloud.atlassian.net";
    private static final String ATLASSIAN_ID_LOGIN = "https://id.atlassian.com/login";
    private static final String TARGET_PAGE = "https://sdecloud.atlassian.net/wiki/spaces/affin/pages/837091329/Environment+Info+-+Azure";
    
    private CookieManager cookieManager;
    private String username;
    private String password;
    
    public static void main(String[] args) {
        AtlassianIdAuthExtractor extractor = new AtlassianIdAuthExtractor();
        
        System.out.println("🔐 Atlassian ID Authentication Extractor");
        System.out.println("=" + "=".repeat(55));
        System.out.println("Target: " + TARGET_PAGE);
        System.out.println("Auth URL: " + ATLASSIAN_ID_LOGIN);
        System.out.println();
        
        // Get credentials from user
        extractor.getCredentials();
        
        // Perform authentication and extraction
        extractor.performAtlassianIdAuth();
    }
    
    public AtlassianIdAuthExtractor() {
        // Setup cookie management for session handling
        this.cookieManager = new CookieManager();
        this.cookieManager.setCookiePolicy(CookiePolicy.ACCEPT_ALL);
        CookieHandler.setDefault(cookieManager);
    }
    
    /**
     * Get username and password from user
     */
    private void getCredentials() {
        Scanner scanner = new Scanner(System.in);
        
        System.out.print("Enter Atlassian username/email: ");
        this.username = scanner.nextLine().trim();
        
        System.out.print("Enter Atlassian password: ");
        this.password = scanner.nextLine().trim();
        
        System.out.println();
        System.out.println("✅ Credentials captured");
        System.out.println("Username: " + username);
        System.out.println("Password: " + "*".repeat(password.length()));
        System.out.println();
    }
    
    /**
     * Perform Atlassian ID authentication flow
     */
    private void performAtlassianIdAuth() {
        try {
            System.out.println("🔐 Starting Atlassian ID Authentication Flow");
            System.out.println("-".repeat(50));
            
            // Step 1: Access the target page to get redirected to login
            System.out.println("📄 Step 1: Accessing target page to get auth redirect...");
            String authUrl = getAuthRedirectUrl();
            
            if (authUrl == null) {
                System.out.println("❌ Failed to get authentication URL");
                return;
            }
            
            System.out.println("✅ Got authentication URL: " + authUrl);
            
            // Step 2: Get the Atlassian ID login page
            System.out.println("\n🔑 Step 2: Getting Atlassian ID login page...");
            String loginPageContent = getAtlassianIdLoginPage(authUrl);
            
            if (loginPageContent == null) {
                System.out.println("❌ Failed to get Atlassian ID login page");
                return;
            }
            
            System.out.println("✅ Login page retrieved: " + loginPageContent.length() + " chars");
            
            // Step 3: Extract authentication tokens and form data
            System.out.println("\n🔍 Step 3: Extracting authentication tokens...");
            Map<String, String> authData = extractAtlassianAuthData(loginPageContent);
            
            if (authData.isEmpty()) {
                System.out.println("❌ Failed to extract authentication data");
                return;
            }
            
            System.out.println("✅ Extracted " + authData.size() + " authentication parameters");
            
            // Step 4: Perform login
            System.out.println("\n🔓 Step 4: Performing Atlassian ID login...");
            boolean loginSuccess = performAtlassianIdLogin(authData);
            
            if (!loginSuccess) {
                System.out.println("❌ Atlassian ID login failed");
                return;
            }
            
            System.out.println("✅ Atlassian ID login successful!");
            
            // Step 5: Follow redirect back to Confluence
            System.out.println("\n🔄 Step 5: Following redirect to Confluence...");
            boolean confluenceAccess = followRedirectToConfluence();
            
            if (!confluenceAccess) {
                System.out.println("❌ Failed to access Confluence after login");
                return;
            }
            
            System.out.println("✅ Successfully authenticated with Confluence!");
            
            // Step 6: Access target page with authenticated session
            System.out.println("\n📄 Step 6: Accessing target page with authenticated session...");
            accessTargetPageWithAuth();
            
        } catch (Exception e) {
            System.out.println("❌ Authentication error: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * Get authentication redirect URL by accessing target page
     */
    private String getAuthRedirectUrl() {
        try {
            HttpURLConnection conn = createConnection(TARGET_PAGE);
            conn.setInstanceFollowRedirects(false); // Handle redirects manually
            
            int responseCode = conn.getResponseCode();
            System.out.println("   Target page response: " + responseCode);
            
            if (responseCode == 302 || responseCode == 301) {
                String location = conn.getHeaderField("Location");
                System.out.println("   Redirect to: " + location);
                return location;
            } else if (responseCode == 200) {
                // Check if page contains redirect meta tag
                String content = readResponse(conn);
                String metaRedirect = extractMetaRedirect(content);
                if (metaRedirect != null) {
                    System.out.println("   Meta redirect to: " + metaRedirect);
                    return metaRedirect;
                }
            }
            
            return null;
            
        } catch (Exception e) {
            System.out.println("   Error getting auth redirect: " + e.getMessage());
            return null;
        }
    }
    
    /**
     * Extract meta redirect URL from HTML
     */
    private String extractMetaRedirect(String html) {
        Pattern pattern = Pattern.compile(
            "meta\\s+http-equiv=[\"']Refresh[\"']\\s+content=[\"'][^\"']*URL=([^\"']+)[\"']",
            Pattern.CASE_INSENSITIVE
        );
        
        Matcher matcher = pattern.matcher(html);
        if (matcher.find()) {
            return matcher.group(1);
        }
        
        return null;
    }
    
    /**
     * Get Atlassian ID login page
     */
    private String getAtlassianIdLoginPage(String authUrl) {
        try {
            HttpURLConnection conn = createConnection(authUrl);
            
            int responseCode = conn.getResponseCode();
            System.out.println("   Atlassian ID response: " + responseCode);
            
            if (responseCode == 200) {
                return readResponse(conn);
            } else {
                System.out.println("   Error response: " + readErrorResponse(conn));
                return null;
            }
            
        } catch (Exception e) {
            System.out.println("   Exception getting login page: " + e.getMessage());
            return null;
        }
    }
    
    /**
     * Extract authentication data from Atlassian ID login page
     */
    private Map<String, String> extractAtlassianAuthData(String html) {
        Map<String, String> authData = new HashMap<>();
        
        // Extract form action URL
        Pattern formPattern = Pattern.compile(
            "<form[^>]*action=[\"']([^\"']+)[\"'][^>]*>",
            Pattern.CASE_INSENSITIVE | Pattern.DOTALL
        );
        
        Matcher formMatcher = formPattern.matcher(html);
        if (formMatcher.find()) {
            authData.put("action", formMatcher.group(1));
            System.out.println("   Form action: " + formMatcher.group(1));
        }
        
        // Extract hidden input fields
        Pattern inputPattern = Pattern.compile(
            "<input[^>]*type=[\"']hidden[\"'][^>]*name=[\"']([^\"']+)[\"'][^>]*value=[\"']([^\"']*)[\"'][^>]*>|" +
            "<input[^>]*name=[\"']([^\"']+)[\"'][^>]*type=[\"']hidden[\"'][^>]*value=[\"']([^\"']*)[\"'][^>]*>",
            Pattern.CASE_INSENSITIVE
        );
        
        Matcher inputMatcher = inputPattern.matcher(html);
        while (inputMatcher.find()) {
            String name = inputMatcher.group(1) != null ? inputMatcher.group(1) : inputMatcher.group(3);
            String value = inputMatcher.group(2) != null ? inputMatcher.group(2) : inputMatcher.group(4);
            
            if (name != null && value != null) {
                authData.put(name, value);
                System.out.println("   Hidden field: " + name + " = " + 
                    (value.length() > 20 ? value.substring(0, 20) + "..." : value));
            }
        }
        
        return authData;
    }
    
    /**
     * Perform Atlassian ID login
     */
    private boolean performAtlassianIdLogin(Map<String, String> authData) {
        try {
            String actionUrl = authData.get("action");
            if (actionUrl == null) {
                System.out.println("   No form action URL found");
                return false;
            }
            
            // Make action URL absolute if needed
            if (actionUrl.startsWith("/")) {
                actionUrl = "https://id.atlassian.com" + actionUrl;
            }
            
            System.out.println("   Posting to: " + actionUrl);
            
            HttpURLConnection conn = createConnection(actionUrl);
            conn.setRequestMethod("POST");
            conn.setDoOutput(true);
            
            // Set form headers
            conn.setRequestProperty("Content-Type", "application/x-www-form-urlencoded");
            conn.setRequestProperty("Referer", ATLASSIAN_ID_LOGIN);
            
            // Prepare form data
            StringBuilder formData = new StringBuilder();
            
            // Add username and password
            formData.append("username=").append(URLEncoder.encode(username, "UTF-8"));
            formData.append("&password=").append(URLEncoder.encode(password, "UTF-8"));
            
            // Add all hidden fields
            for (Map.Entry<String, String> entry : authData.entrySet()) {
                if (!"action".equals(entry.getKey())) {
                    formData.append("&").append(entry.getKey()).append("=")
                           .append(URLEncoder.encode(entry.getValue(), "UTF-8"));
                }
            }
            
            System.out.println("   Form data length: " + formData.length() + " chars");
            
            // Send login request
            try (OutputStream os = conn.getOutputStream()) {
                os.write(formData.toString().getBytes(StandardCharsets.UTF_8));
            }
            
            int responseCode = conn.getResponseCode();
            System.out.println("   Login response: " + responseCode);
            
            // Check for successful login (usually redirect)
            if (responseCode == 302 || responseCode == 301) {
                String location = conn.getHeaderField("Location");
                System.out.println("   Redirect to: " + location);
                return location != null && location.contains("sdecloud.atlassian.net");
            } else if (responseCode == 200) {
                String response = readResponse(conn);
                // Check if response indicates success
                return !response.contains("error") && !response.contains("incorrect") && 
                       !response.contains("invalid");
            }
            
            return false;
            
        } catch (Exception e) {
            System.out.println("   Login exception: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * Follow redirect back to Confluence
     */
    private boolean followRedirectToConfluence() {
        try {
            // Try accessing the original target page again
            HttpURLConnection conn = createConnection(TARGET_PAGE);
            
            int responseCode = conn.getResponseCode();
            System.out.println("   Confluence access response: " + responseCode);
            
            if (responseCode == 200) {
                String content = readResponse(conn);
                
                // Check if we got actual content (not redirect page)
                if (!content.contains("id.atlassian.com") && 
                    !content.contains("meta http-equiv=\"Refresh\"") &&
                    content.length() > 100000) { // Substantial content
                    
                    System.out.println("   ✅ Got authenticated Confluence content: " + content.length() + " chars");
                    return true;
                }
            }
            
            return false;
            
        } catch (Exception e) {
            System.out.println("   Confluence access exception: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * Access target page with authenticated session
     */
    private void accessTargetPageWithAuth() {
        try {
            // Try multiple approaches to get the content
            tryDirectPageAccess();
            tryContentViewModes();
            tryApiEndpointsWithSession();
            
        } catch (Exception e) {
            System.out.println("❌ Target page access error: " + e.getMessage());
        }
    }
    
    /**
     * Try direct page access
     */
    private void tryDirectPageAccess() {
        System.out.println("🌐 Trying direct page access...");
        
        try {
            HttpURLConnection conn = createConnection(TARGET_PAGE);
            int responseCode = conn.getResponseCode();
            
            if (responseCode == 200) {
                String content = readResponse(conn);
                System.out.println("   Content size: " + content.length() + " chars");
                
                if (isActualContent(content)) {
                    System.out.println("   ✅ SUCCESS! Got actual page content");
                    extractAndSaveUrls(content, "atlassian_id_direct_content.html");
                } else {
                    System.out.println("   ⚠️  Still getting shell/redirect page");
                    saveDebugContent(content, "atlassian_id_direct_debug.html");
                }
            } else {
                System.out.println("   Response code: " + responseCode);
            }
            
        } catch (Exception e) {
            System.out.println("   Error: " + e.getMessage());
        }
    }
    
    /**
     * Try different content view modes
     */
    private void tryContentViewModes() {
        System.out.println("👁️ Trying content view modes...");
        
        String[] viewModes = {
            TARGET_PAGE + "?view=content",
            TARGET_PAGE + "?mode=view", 
            TARGET_PAGE + "?decorator=printable",
            TARGET_PAGE + "?theme=documentation",
            TARGET_PAGE + "?print=content"
        };
        
        for (String url : viewModes) {
            try {
                System.out.println("   Testing: " + url.substring(url.indexOf('?')));
                
                HttpURLConnection conn = createConnection(url);
                int responseCode = conn.getResponseCode();
                
                if (responseCode == 200) {
                    String content = readResponse(conn);
                    System.out.println("     Size: " + content.length() + " chars");
                    
                    if (isActualContent(content)) {
                        System.out.println("     ✅ SUCCESS with view mode!");
                        extractAndSaveUrls(content, "atlassian_id_view_content.html");
                        return;
                    }
                }
                
            } catch (Exception e) {
                System.out.println("     Error: " + e.getMessage());
            }
        }
    }
    
    /**
     * Try API endpoints with session cookies
     */
    private void tryApiEndpointsWithSession() {
        System.out.println("🔧 Trying API endpoints with session...");
        
        String[] apiEndpoints = {
            "/wiki/rest/api/content/837091329?expand=body.storage",
            "/wiki/rest/api/content/837091329?expand=body.view",
            "/wiki/rest/api/content/837091329?expand=body.export_view"
        };
        
        for (String endpoint : apiEndpoints) {
            try {
                System.out.println("   Testing API: " + endpoint.substring(endpoint.lastIndexOf('/') + 1));
                
                HttpURLConnection conn = createConnection(CONFLUENCE_URL + endpoint);
                conn.setRequestProperty("Accept", "application/json");
                
                int responseCode = conn.getResponseCode();
                if (responseCode == 200) {
                    String content = readResponse(conn);
                    System.out.println("     API Success: " + content.length() + " chars");
                    
                    if (content.contains("\"body\"") || content.contains("\"storage\"")) {
                        System.out.println("     ✅ SUCCESS! Found body content in API");
                        extractContentFromJson(content, "atlassian_id_api_content.json");
                        return;
                    }
                }
                
            } catch (Exception e) {
                System.out.println("     API error: " + e.getMessage());
            }
        }
    }
    
    /**
     * Check if content is actual page content
     */
    private boolean isActualContent(String content) {
        return content.contains("Environment Info") || 
               content.contains("Azure") ||
               (content.contains("<a") && content.contains("href") && 
                !content.contains("id.atlassian.com") && 
                !content.contains("meta http-equiv=\"Refresh\"") &&
                content.length() > 100000); // Substantial content
    }
    
    /**
     * Extract and save URLs from HTML content
     */
    private void extractAndSaveUrls(String content, String filename) {
        try {
            // Save full content
            try (FileWriter writer = new FileWriter(filename, StandardCharsets.UTF_8)) {
                writer.write(content);
            }
            System.out.println("   💾 Saved content to: " + filename);
            
            // Extract URLs
            Pattern linkPattern = Pattern.compile("<a[^>]*href=[\"']([^\"']*)[\"'][^>]*>(.*?)</a>", 
                Pattern.CASE_INSENSITIVE | Pattern.DOTALL);
            Matcher matcher = linkPattern.matcher(content);
            
            Set<String> urls = new HashSet<>();
            StringBuilder linksList = new StringBuilder();
            linksList.append("URLs extracted from: ").append(filename).append("\n");
            linksList.append("=" + "=".repeat(60)).append("\n\n");
            
            while (matcher.find()) {
                String href = matcher.group(1);
                String text = matcher.group(2).replaceAll("<[^>]+>", "").trim();
                
                if (!href.startsWith("#") && !href.startsWith("javascript:") && !urls.contains(href)) {
                    urls.add(href);
                    
                    if (text.length() > 100) {
                        text = text.substring(0, 100) + "...";
                    }
                    
                    linksList.append(String.format("%3d. %s\n", urls.size(), href));
                    linksList.append(String.format("     Text: %s\n\n", text));
                }
            }
            
            // Save URLs
            String linksFile = filename.replace(".html", "_links.txt");
            try (FileWriter writer = new FileWriter(linksFile, StandardCharsets.UTF_8)) {
                writer.write(linksList.toString());
            }
            
            System.out.println("   🔗 Extracted " + urls.size() + " unique URLs to: " + linksFile);
            
            if (urls.size() > 10) {
                System.out.println("   🎉 SUCCESS! Found substantial content with many links!");
            }
            
        } catch (Exception e) {
            System.out.println("   ⚠️  URL extraction error: " + e.getMessage());
        }
    }
    
    /**
     * Extract content from JSON API response
     */
    private void extractContentFromJson(String jsonContent, String filename) {
        try {
            // Save JSON response
            try (FileWriter writer = new FileWriter(filename, StandardCharsets.UTF_8)) {
                writer.write(jsonContent);
            }
            System.out.println("   💾 Saved JSON to: " + filename);
            
            // Extract HTML from JSON
            Pattern bodyPattern = Pattern.compile("\"value\"\\s*:\\s*\"(.*?)\"", Pattern.DOTALL);
            Matcher matcher = bodyPattern.matcher(jsonContent);
            
            if (matcher.find()) {
                String htmlContent = matcher.group(1);
                // Unescape JSON string
                htmlContent = htmlContent.replace("\\\"", "\"")
                                       .replace("\\n", "\n")
                                       .replace("\\r", "\r")
                                       .replace("\\t", "\t")
                                       .replace("\\\\", "\\");
                
                System.out.println("   📄 Extracted HTML: " + htmlContent.length() + " chars");
                
                // Save HTML and extract URLs
                String htmlFile = filename.replace(".json", "_body.html");
                extractAndSaveUrls(htmlContent, htmlFile);
            }
            
        } catch (Exception e) {
            System.out.println("   ⚠️  JSON extraction error: " + e.getMessage());
        }
    }
    
    /**
     * Save debug content
     */
    private void saveDebugContent(String content, String filename) {
        try {
            String sample = content.length() > 5000 ? content.substring(0, 5000) : content;
            try (FileWriter writer = new FileWriter(filename, StandardCharsets.UTF_8)) {
                writer.write("Debug content sample:\n");
                writer.write("=" + "=".repeat(50) + "\n\n");
                writer.write(sample);
            }
            System.out.println("   💾 Saved debug content to: " + filename);
        } catch (Exception e) {
            System.out.println("   ⚠️  Debug save error: " + e.getMessage());
        }
    }
    
    /**
     * Create HTTP connection with session cookies
     */
    private HttpURLConnection createConnection(String url) throws IOException {
        URL urlObj = new URL(url);
        HttpURLConnection conn = (HttpURLConnection) urlObj.openConnection();
        
        // Browser-like headers
        conn.setRequestProperty("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36");
        conn.setRequestProperty("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8");
        conn.setRequestProperty("Accept-Language", "en-US,en;q=0.5");
        conn.setRequestProperty("Accept-Encoding", "gzip, deflate, br");
        conn.setRequestProperty("Connection", "keep-alive");
        conn.setRequestProperty("Upgrade-Insecure-Requests", "1");
        
        // Security headers
        conn.setRequestProperty("Sec-Fetch-Dest", "document");
        conn.setRequestProperty("Sec-Fetch-Mode", "navigate");
        conn.setRequestProperty("Sec-Fetch-Site", "same-origin");
        
        conn.setConnectTimeout(15000);
        conn.setReadTimeout(30000);
        
        return conn;
    }
    
    /**
     * Read response handling compression
     */
    private String readResponse(HttpURLConnection conn) throws IOException {
        InputStream inputStream = conn.getInputStream();
        
        String encoding = conn.getContentEncoding();
        if ("gzip".equals(encoding)) {
            inputStream = new java.util.zip.GZIPInputStream(inputStream);
        }
        
        try (BufferedReader reader = new BufferedReader(
                new InputStreamReader(inputStream, StandardCharsets.UTF_8))) {
            StringBuilder response = new StringBuilder();
            String line;
            while ((line = reader.readLine()) != null) {
                response.append(line).append("\n");
            }
            return response.toString();
        }
    }
    
    /**
     * Read error response
     */
    private String readErrorResponse(HttpURLConnection conn) {
        try (BufferedReader reader = new BufferedReader(
                new InputStreamReader(conn.getErrorStream(), StandardCharsets.UTF_8))) {
            StringBuilder response = new StringBuilder();
            String line;
            while ((line = reader.readLine()) != null) {
                response.append(line).append("\n");
            }
            return response.toString();
        } catch (Exception e) {
            return "Could not read error response: " + e.getMessage();
        }
    }
}
