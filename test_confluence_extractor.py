"""
Unit tests for Confluence URL Extractor
"""
import unittest
import tempfile
import json
import csv
from pathlib import Path
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime

from config import ConfluenceConfig
from url_extractor import URLExtractor, URLMetadata
from data_exporter import DataExporter
from validators import (
    validate_confluence_url, validate_email, validate_api_token,
    validate_page_id, validate_timeout
)
from exceptions import ValidationError


class TestValidators(unittest.TestCase):
    """Test input validation functions"""
    
    def test_validate_confluence_url(self):
        """Test URL validation"""
        # Valid URLs
        self.assertEqual(validate_confluence_url("https://company.atlassian.net"), "https://company.atlassian.net")
        self.assertEqual(validate_confluence_url("company.atlassian.net"), "https://company.atlassian.net")

        # Invalid URLs
        with self.assertRaises(ValidationError):
            validate_confluence_url("")
        with self.assertRaises(ValidationError):
            validate_confluence_url("://invalid")
    
    def test_validate_email(self):
        """Test email validation"""
        # Valid emails
        self.assertEqual(validate_email("<EMAIL>"), "<EMAIL>")
        self.assertEqual(validate_email("<EMAIL>"), "<EMAIL>")
        
        # Invalid emails
        with self.assertRaises(ValidationError):
            validate_email("")
        with self.assertRaises(ValidationError):
            validate_email("invalid-email")
    
    def test_validate_api_token(self):
        """Test API token validation"""
        # Valid token
        valid_token = "a" * 20
        self.assertEqual(validate_api_token(valid_token), valid_token)
        
        # Invalid tokens
        with self.assertRaises(ValidationError):
            validate_api_token("")
        with self.assertRaises(ValidationError):
            validate_api_token("short")
    
    def test_validate_page_id(self):
        """Test page ID validation"""
        # Valid page IDs
        self.assertEqual(validate_page_id("123456"), "123456")
        self.assertEqual(validate_page_id("abc123"), "abc123")
        
        # Invalid page IDs
        with self.assertRaises(ValidationError):
            validate_page_id("")
        with self.assertRaises(ValidationError):
            validate_page_id("invalid-id!")
    
    def test_validate_timeout(self):
        """Test timeout validation"""
        # Valid timeouts
        self.assertEqual(validate_timeout(30), 30)
        self.assertEqual(validate_timeout(300), 300)
        
        # Invalid timeouts
        with self.assertRaises(ValidationError):
            validate_timeout(0)
        with self.assertRaises(ValidationError):
            validate_timeout(400)


class TestURLExtractor(unittest.TestCase):
    """Test URL extraction functionality"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.extractor = URLExtractor("https://company.atlassian.net")
        self.sample_html = """
        <html>
        <body>
            <a href="https://example.com">External Link</a>
            <a href="/internal/page">Internal Link</a>
            <a href="mailto:<EMAIL>">Email Link</a>
            <a href="#anchor">Anchor Link</a>
            <a href="javascript:void(0)">JavaScript Link</a>
            <a href="https://company.atlassian.net/wiki/spaces/TEST">Confluence Link</a>
        </body>
        </html>
        """
    
    def test_extract_urls_from_html(self):
        """Test URL extraction from HTML"""
        urls = self.extractor.extract_urls_from_html(self.sample_html)
        
        # Should extract valid URLs (excluding anchor and javascript)
        self.assertGreater(len(urls), 0)
        
        # Check that we have different types of URLs
        url_texts = [url.text for url in urls]
        self.assertIn("External Link", url_texts)
        self.assertIn("Internal Link", url_texts)
    
    def test_url_classification(self):
        """Test URL type classification"""
        urls = self.extractor.extract_urls_from_html(self.sample_html)
        
        # Find specific URLs and check their classification
        for url in urls:
            if url.text == "External Link":
                self.assertTrue(url.is_external)
                self.assertFalse(url.is_confluence_link)
            elif url.text == "Confluence Link":
                self.assertTrue(url.is_confluence_link)
    
    def test_categorize_urls(self):
        """Test URL categorization by domain"""
        urls = self.extractor.extract_urls_from_html(self.sample_html)
        categorized = self.extractor.categorize_urls(urls)
        
        self.assertIsInstance(categorized, dict)
        self.assertGreater(len(categorized), 0)
    
    def test_get_extraction_summary(self):
        """Test extraction summary generation"""
        urls = self.extractor.extract_urls_from_html(self.sample_html)
        summary = self.extractor.get_extraction_summary(urls)
        
        self.assertIn('total_urls', summary)
        self.assertIn('external_urls', summary)
        self.assertIn('internal_urls', summary)
        self.assertIn('unique_domains', summary)
        self.assertEqual(summary['total_urls'], len(urls))


class TestDataExporter(unittest.TestCase):
    """Test data export functionality"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.temp_dir = tempfile.mkdtemp()
        self.exporter = DataExporter(self.temp_dir)
        
        # Create sample URL metadata
        self.sample_urls = [
            URLMetadata(
                url="https://example.com",
                text="Example Link",
                domain="example.com",
                link_type="external",
                is_external=True
            ),
            URLMetadata(
                url="https://internal.com/page",
                text="Internal Link",
                domain="internal.com",
                link_type="internal",
                is_external=False
            )
        ]
    
    def test_export_urls_to_json(self):
        """Test JSON export"""
        file_path = self.exporter.export_urls_to_json(self.sample_urls, "test.json")
        
        # Check file exists
        self.assertTrue(Path(file_path).exists())
        
        # Check content
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        self.assertIn('urls', data)
        self.assertEqual(len(data['urls']), len(self.sample_urls))
    
    def test_export_urls_to_csv(self):
        """Test CSV export"""
        file_path = self.exporter.export_urls_to_csv(self.sample_urls, "test.csv")
        
        # Check file exists
        self.assertTrue(Path(file_path).exists())
        
        # Check content
        with open(file_path, 'r', encoding='utf-8') as f:
            reader = csv.reader(f)
            rows = list(reader)
        
        # Should have header + data rows
        self.assertEqual(len(rows), len(self.sample_urls) + 1)
    
    def test_export_categorized_urls(self):
        """Test categorized URL export"""
        categorized = {
            "example.com": [self.sample_urls[0]],
            "internal.com": [self.sample_urls[1]]
        }
        
        json_path = self.exporter.export_categorized_urls_to_json(categorized, "categorized.json")
        csv_path = self.exporter.export_categorized_urls_to_csv(categorized, "categorized.csv")
        
        # Check files exist
        self.assertTrue(Path(json_path).exists())
        self.assertTrue(Path(csv_path).exists())
    
    def test_export_all_formats(self):
        """Test exporting all formats"""
        categorized = {
            "example.com": [self.sample_urls[0]],
            "internal.com": [self.sample_urls[1]]
        }
        summary = {
            "total_urls": 2,
            "external_urls": 1,
            "internal_urls": 1
        }
        
        exports = self.exporter.export_all_formats(
            self.sample_urls, categorized, summary, "test"
        )
        
        # Check all export types are present
        expected_types = ['raw_json', 'raw_csv', 'categorized_json', 'categorized_csv', 'summary_json']
        for export_type in expected_types:
            self.assertIn(export_type, exports)
            self.assertTrue(Path(exports[export_type]).exists())


class TestURLMetadata(unittest.TestCase):
    """Test URLMetadata class"""
    
    def test_url_metadata_creation(self):
        """Test URLMetadata object creation"""
        metadata = URLMetadata(
            url="https://example.com",
            text="Example Link",
            title="Example Title"
        )
        
        self.assertEqual(metadata.url, "https://example.com")
        self.assertEqual(metadata.text, "Example Link")
        self.assertEqual(metadata.title, "Example Title")
        self.assertEqual(metadata.domain, "example.com")
    
    def test_url_metadata_to_dict(self):
        """Test URLMetadata dictionary conversion"""
        metadata = URLMetadata(
            url="https://example.com",
            text="Example Link"
        )
        
        data_dict = metadata.to_dict()
        
        self.assertIsInstance(data_dict, dict)
        self.assertEqual(data_dict['url'], "https://example.com")
        self.assertEqual(data_dict['text'], "Example Link")


class TestConfig(unittest.TestCase):
    """Test configuration management"""
    
    def test_config_validation(self):
        """Test configuration validation"""
        # Valid config
        config = ConfluenceConfig(
            confluence_url="https://company.atlassian.net",
            username="<EMAIL>",
            api_token="valid_token_123456789"
        )
        
        self.assertEqual(config.confluence_url, "https://company.atlassian.net")
        self.assertEqual(config.username, "<EMAIL>")
    
    def test_config_url_validation(self):
        """Test URL validation in config"""
        with self.assertRaises(ValueError):
            ConfluenceConfig(confluence_url="invalid-url")


if __name__ == '__main__':
    unittest.main()
