#!/usr/bin/env python3
"""
Debug the v2 API endpoint with different authentication methods
"""
import requests
from requests.auth import HTTPBasicAuth
import json

def test_v2_api():
    """Test the v2 API endpoint with different authentication approaches"""
    
    page_id = "837091329"
    base_url = "https://sdecloud.atlassian.net"
    
    # Get credentials
    username = input("Enter username (try both 'imitiyaz' and full email): ").strip()
    api_token = input("Enter API token: ").strip()
    
    print(f"🔍 Testing v2 API for page ID: {page_id}")
    print(f"   Username: {username}")
    print(f"   Token: {api_token[:10]}...{api_token[-4:]}")
    print()
    
    # Test different endpoint variations
    endpoints_to_test = [
        f"/wiki/api/v2/pages/{page_id}",
        f"/wiki/api/v2/pages/{page_id}?body-format=storage",
        f"/wiki/api/v2/pages/{page_id}?body-format=view",
        f"/wiki/api/v2/pages/{page_id}?expand=body",
        f"/api/v2/pages/{page_id}",  # Without /wiki prefix
    ]
    
    for endpoint in endpoints_to_test:
        print(f"🧪 Testing endpoint: {endpoint}")
        url = f"{base_url}{endpoint}"
        print(f"   Full URL: {url}")
        
        try:
            # Test with HTTPBasicAuth
            response = requests.get(
                url,
                auth=HTTPBasicAuth(username, api_token),
                headers={"Accept": "application/json"},
                timeout=10
            )
            
            print(f"   Status: {response.status_code}")
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    print(f"   ✅ SUCCESS!")
                    print(f"   Title: {data.get('title', 'N/A')}")
                    print(f"   ID: {data.get('id', 'N/A')}")
                    print(f"   Status: {data.get('status', 'N/A')}")
                    
                    # Check for body content
                    if 'body' in data:
                        print(f"   Body available: {bool(data['body'])}")
                        if data['body']:
                            body_keys = list(data['body'].keys()) if isinstance(data['body'], dict) else []
                            print(f"   Body keys: {body_keys}")
                    
                    # Check for links
                    links = data.get('_links', {})
                    if links:
                        print(f"   Links: {list(links.keys())}")
                        webui = links.get('webui', '')
                        if webui:
                            full_page_url = f"{base_url}{webui}"
                            print(f"   Page URL: {full_page_url}")
                    
                    print(f"   🎉 This endpoint works!")
                    
                    # Try to get the body content if not included
                    if 'body' not in data or not data['body']:
                        print(f"   🔍 Trying to get body content...")
                        body_endpoint = f"{endpoint}?body-format=storage"
                        body_url = f"{base_url}{body_endpoint}"
                        
                        body_response = requests.get(
                            body_url,
                            auth=HTTPBasicAuth(username, api_token),
                            headers={"Accept": "application/json"},
                            timeout=10
                        )
                        
                        if body_response.status_code == 200:
                            body_data = body_response.json()
                            if 'body' in body_data and body_data['body']:
                                print(f"   ✅ Got body content!")
                                body_content = body_data['body']
                                if isinstance(body_content, dict):
                                    for format_name, content in body_content.items():
                                        if isinstance(content, dict) and 'value' in content:
                                            html_content = content['value']
                                            print(f"   {format_name} content: {len(html_content)} chars")
                                            
                                            # Count links in the content
                                            import re
                                            links = re.findall(r'<a[^>]*href=["\']([^"\']*)["\'][^>]*>', html_content)
                                            print(f"   Links found in {format_name}: {len(links)}")
                                            if links:
                                                print(f"   Sample links: {links[:3]}")
                    
                    return endpoint, data
                    
                except Exception as e:
                    print(f"   ⚠️  JSON parsing error: {str(e)}")
                    print(f"   Raw response: {response.text[:200]}...")
                    
            elif response.status_code == 401:
                print(f"   ❌ Authentication failed")
            elif response.status_code == 403:
                print(f"   ❌ Access forbidden")
            elif response.status_code == 404:
                print(f"   ❌ Not found")
                try:
                    error_data = response.json()
                    print(f"   Error details: {error_data}")
                except:
                    print(f"   Raw error: {response.text[:200]}")
            else:
                print(f"   ❌ Error: {response.text[:200]}")
                
        except Exception as e:
            print(f"   ❌ Exception: {str(e)}")
        
        print()
    
    print("❌ No working endpoint found")
    return None, None

if __name__ == "__main__":
    test_v2_api()
