#!/usr/bin/env python3
"""
Test the exact v2 API endpoint that works in browser
"""
import requests
from requests.auth import HTTPBasicAuth
import json

def test_exact_endpoint():
    """Test the exact endpoint that works in browser"""
    
    # Exact same URL that works in browser
    url = "https://sdecloud.atlassian.net/wiki/api/v2/pages/837091329"
    
    # Same credentials from your script
    username = "imitiyaz"
    api_token = "ATATT3xFfGF0hpe-8j9m0miV0UUVy6JaMzsgbXht2AVRI7mPn9sekwyqo9v8yPpx9Ee3kyj8LWWiC9usd0GBSa3Wb0etrIvmqnvNf5oqRiP379Jf1me5DhggNQ_763UlHA0pGnnE6zXIHVOupY_sytIeX756FPaHoGA_pfmzRbHhi7ZPcswMvTak=6D360167"
    
    print(f"🔍 Testing exact v2 API endpoint...")
    print(f"   URL: {url}")
    print(f"   Username: {username}")
    print()
    
    try:
        # Make the request with detailed debugging
        auth = HTTPBasicAuth(username, api_token)
        headers = {"Accept": "application/json"}
        
        print("📤 Making request...")
        response = requests.get(url, auth=auth, headers=headers, timeout=10)
        
        print(f"📥 Response received:")
        print(f"   Status Code: {response.status_code}")
        print(f"   Headers: {dict(response.headers)}")
        print()
        
        if response.status_code == 200:
            print("✅ SUCCESS! API is working")
            data = response.json()
            print(f"   Title: {data.get('title', 'N/A')}")
            print(f"   ID: {data.get('id', 'N/A')}")
            print(f"   Status: {data.get('status', 'N/A')}")
            
            # Now try to get the body content
            print(f"\n🔍 Trying to get body content...")
            body_url = f"{url}?body-format=storage"
            print(f"   Body URL: {body_url}")
            
            body_response = requests.get(body_url, auth=auth, headers=headers, timeout=10)
            print(f"   Body Status: {body_response.status_code}")
            
            if body_response.status_code == 200:
                body_data = body_response.json()
                print(f"   ✅ Got body data!")
                
                # Check if body content is available
                if 'body' in body_data:
                    body = body_data['body']
                    print(f"   Body type: {type(body)}")
                    print(f"   Body keys: {list(body.keys()) if isinstance(body, dict) else 'Not a dict'}")
                    
                    # Look for storage format
                    if isinstance(body, dict) and 'storage' in body:
                        storage = body['storage']
                        if isinstance(storage, dict) and 'value' in storage:
                            html_content = storage['value']
                            print(f"   ✅ Found HTML content: {len(html_content)} characters")
                            
                            # Count links
                            import re
                            links = re.findall(r'<a[^>]*href=["\']([^"\']*)["\'][^>]*>', html_content)
                            print(f"   🔗 Links found: {len(links)}")
                            
                            if links:
                                print(f"   Sample links:")
                                for i, link in enumerate(links[:10], 1):
                                    print(f"      {i}. {link}")
                                
                                # Save content for inspection
                                with open('v2_api_content.html', 'w', encoding='utf-8') as f:
                                    f.write(html_content)
                                print(f"   💾 Saved content to: v2_api_content.html")
                                
                                return True, html_content
                            else:
                                print(f"   ⚠️  No links found in content")
                                # Save content anyway for inspection
                                with open('v2_api_content_no_links.html', 'w', encoding='utf-8') as f:
                                    f.write(html_content)
                                print(f"   💾 Saved content to: v2_api_content_no_links.html")
                        else:
                            print(f"   ❌ Storage format not found in body")
                    else:
                        print(f"   ❌ Storage not found in body")
                else:
                    print(f"   ❌ No body in response")
            else:
                print(f"   ❌ Body request failed: {body_response.text[:200]}")
        
        else:
            print(f"❌ Request failed with status {response.status_code}")
            print(f"   Response: {response.text}")
            
            # Check if it's an authentication issue
            if response.status_code == 401:
                print(f"   🔐 Authentication failed - check API token")
            elif response.status_code == 403:
                print(f"   🚫 Access forbidden - check permissions")
            elif response.status_code == 404:
                print(f"   🔍 Not found - check page ID and URL")
    
    except Exception as e:
        print(f"❌ Exception occurred: {str(e)}")
        import traceback
        traceback.print_exc()
    
    return False, None

if __name__ == "__main__":
    success, content = test_exact_endpoint()
    if success:
        print(f"\n🎉 Successfully extracted content with links!")
    else:
        print(f"\n❌ Failed to extract content")
