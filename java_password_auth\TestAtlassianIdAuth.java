import java.io.*;
import java.net.*;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Test Atlassian ID authentication flow
 */
public class TestAtlassianIdAuth {
    
    private static final String CONFLUENCE_URL = "https://sdecloud.atlassian.net";
    private static final String TARGET_PAGE = "https://sdecloud.atlassian.net/wiki/spaces/affin/pages/837091329/Environment+Info+-+Azure";
    
    private CookieManager cookieManager;
    
    public static void main(String[] args) {
        TestAtlassianIdAuth tester = new TestAtlassianIdAuth();
        
        System.out.println("🔐 Atlassian ID Authentication Flow Test");
        System.out.println("=" + "=".repeat(55));
        System.out.println("Target: " + TARGET_PAGE);
        System.out.println();
        
        tester.testAtlassianIdFlow();
    }
    
    public TestAtlassianIdAuth() {
        this.cookieManager = new CookieManager();
        this.cookieManager.setCookiePolicy(CookiePolicy.ACCEPT_ALL);
        CookieHandler.setDefault(cookieManager);
    }
    
    /**
     * Test the Atlassian ID authentication flow
     */
    private void testAtlassianIdFlow() {
        try {
            System.out.println("🔍 Testing Atlassian ID Authentication Flow");
            System.out.println("-".repeat(50));
            
            // Step 1: Test target page access to get redirect
            testTargetPageRedirect();
            
            // Step 2: Test Atlassian ID login page access
            testAtlassianIdLoginPage();
            
            // Step 3: Show the complete authentication flow
            showCompleteAuthFlow();
            
        } catch (Exception e) {
            System.out.println("❌ Test error: " + e.getMessage());
        }
    }
    
    /**
     * Test accessing target page to get redirect
     */
    private void testTargetPageRedirect() {
        System.out.println("📄 Step 1: Testing Target Page Redirect");
        
        try {
            HttpURLConnection conn = createConnection(TARGET_PAGE);
            conn.setInstanceFollowRedirects(false); // Handle redirects manually
            
            int responseCode = conn.getResponseCode();
            System.out.println("   Target page response: " + responseCode);
            
            if (responseCode == 302 || responseCode == 301) {
                String location = conn.getHeaderField("Location");
                System.out.println("   ✅ Redirect to: " + location);
                
                if (location != null && location.contains("id.atlassian.com")) {
                    System.out.println("   🎉 Confirmed: Uses Atlassian ID authentication!");
                    
                    // Parse the redirect URL
                    if (location.contains("continue=")) {
                        String continueUrl = extractContinueUrl(location);
                        System.out.println("   Continue URL: " + continueUrl);
                    }
                }
                
            } else if (responseCode == 200) {
                String content = readResponse(conn);
                System.out.println("   Content size: " + content.length() + " chars");
                
                // Check for meta redirect
                String metaRedirect = extractMetaRedirect(content);
                if (metaRedirect != null) {
                    System.out.println("   ✅ Meta redirect to: " + metaRedirect);
                    
                    if (metaRedirect.contains("id.atlassian.com")) {
                        System.out.println("   🎉 Confirmed: Uses Atlassian ID authentication!");
                    }
                }
                
                // Save sample content for analysis
                saveDebugContent(content, "target_page_response.html");
                
            } else {
                System.out.println("   ⚠️  Unexpected response code: " + responseCode);
            }
            
        } catch (Exception e) {
            System.out.println("   ❌ Target page test failed: " + e.getMessage());
        }
        
        System.out.println();
    }
    
    /**
     * Test Atlassian ID login page access
     */
    private void testAtlassianIdLoginPage() {
        System.out.println("🔑 Step 2: Testing Atlassian ID Login Page");
        
        // Use the URL you provided
        String atlassianIdUrl = "https://id.atlassian.com/login?application=confluence&continue=https%3A%2F%2Fsdecloud.atlassian.net%2Fwiki%2Fspaces%2Faffin%2Fpages%2F837091329%2FEnvironment%2BInfo%2B-%2BAzure";
        
        try {
            HttpURLConnection conn = createConnection(atlassianIdUrl);
            
            int responseCode = conn.getResponseCode();
            System.out.println("   Atlassian ID response: " + responseCode);
            
            if (responseCode == 200) {
                String content = readResponse(conn);
                System.out.println("   ✅ Login page accessible: " + content.length() + " chars");
                
                // Analyze login form
                analyzeAtlassianIdForm(content);
                
                // Save login page for analysis
                saveDebugContent(content, "atlassian_id_login_page.html");
                
            } else {
                System.out.println("   ⚠️  Login page not accessible: " + responseCode);
                String errorContent = readErrorResponse(conn);
                System.out.println("   Error: " + errorContent.substring(0, Math.min(200, errorContent.length())));
            }
            
        } catch (Exception e) {
            System.out.println("   ❌ Atlassian ID test failed: " + e.getMessage());
        }
        
        System.out.println();
    }
    
    /**
     * Analyze Atlassian ID login form
     */
    private void analyzeAtlassianIdForm(String content) {
        System.out.println("   🔍 Analyzing login form...");
        
        // Check for form elements
        boolean hasForm = content.contains("<form");
        boolean hasUsernameField = content.contains("username") || content.contains("email");
        boolean hasPasswordField = content.contains("password");
        boolean hasSubmitButton = content.contains("submit") || content.contains("Log in") || content.contains("Sign in");
        
        System.out.println("   Form analysis:");
        System.out.println("     Login form: " + (hasForm ? "✅ Found" : "❌ Missing"));
        System.out.println("     Username field: " + (hasUsernameField ? "✅ Found" : "❌ Missing"));
        System.out.println("     Password field: " + (hasPasswordField ? "✅ Found" : "❌ Missing"));
        System.out.println("     Submit button: " + (hasSubmitButton ? "✅ Found" : "❌ Missing"));
        
        // Extract form action
        Pattern formPattern = Pattern.compile(
            "<form[^>]*action=[\"']([^\"']+)[\"'][^>]*>",
            Pattern.CASE_INSENSITIVE | Pattern.DOTALL
        );
        
        Matcher formMatcher = formPattern.matcher(content);
        if (formMatcher.find()) {
            String action = formMatcher.group(1);
            System.out.println("     Form action: " + action);
        }
        
        // Count hidden fields
        Pattern hiddenPattern = Pattern.compile(
            "<input[^>]*type=[\"']hidden[\"'][^>]*>",
            Pattern.CASE_INSENSITIVE
        );
        
        Matcher hiddenMatcher = hiddenPattern.matcher(content);
        int hiddenCount = 0;
        while (hiddenMatcher.find()) {
            hiddenCount++;
        }
        
        System.out.println("     Hidden fields: " + hiddenCount + " found");
        
        if (hasForm && hasUsernameField && hasPasswordField) {
            System.out.println("   🎉 Atlassian ID login form is properly accessible!");
        }
    }
    
    /**
     * Show complete authentication flow
     */
    private void showCompleteAuthFlow() {
        System.out.println("🚀 Step 3: Complete Atlassian ID Authentication Flow");
        System.out.println("-".repeat(50));
        
        System.out.println("With real credentials, the complete flow would be:");
        System.out.println();
        
        System.out.println("1. 📄 Access target page:");
        System.out.println("   GET " + TARGET_PAGE);
        System.out.println("   Result: 302 Redirect to Atlassian ID");
        System.out.println();
        
        System.out.println("2. 🔑 Get Atlassian ID login page:");
        System.out.println("   GET https://id.atlassian.com/login?application=confluence&continue=...");
        System.out.println("   Extract: Form action, hidden fields, CSRF tokens");
        System.out.println();
        
        System.out.println("3. 🔐 Submit credentials to Atlassian ID:");
        System.out.println("   POST https://id.atlassian.com/login (or form action URL)");
        System.out.println("   Data: username=YOUR_USERNAME");
        System.out.println("         password=YOUR_PASSWORD");
        System.out.println("         [all hidden fields and tokens]");
        System.out.println();
        
        System.out.println("4. 🍪 Get authentication cookies:");
        System.out.println("   Response: 302 Redirect back to Confluence");
        System.out.println("   Cookies: Atlassian session cookies");
        System.out.println();
        
        System.out.println("5. 🔄 Follow redirect to Confluence:");
        System.out.println("   GET " + TARGET_PAGE);
        System.out.println("   Headers: Cookie: [Atlassian session cookies]");
        System.out.println("   Result: ✅ Authenticated access to Confluence!");
        System.out.println();
        
        System.out.println("6. 📄 Access target page content:");
        System.out.println("   GET " + TARGET_PAGE + "?view=content");
        System.out.println("   Result: ✅ Actual page content with all URLs!");
        System.out.println();
        
        System.out.println("🎯 Expected Results:");
        System.out.println("   ✅ Status 200 (not redirect to login)");
        System.out.println("   ✅ Large content size (1MB+ instead of 88KB)");
        System.out.println("   ✅ Many URLs extracted (50+ instead of 2)");
        System.out.println("   ✅ Actual page content (Environment Info - Azure)");
        System.out.println();
        
        System.out.println("📋 To run with real credentials:");
        System.out.println("   java AtlassianIdAuthExtractor");
        System.out.println("   Enter your Atlassian username/email and password when prompted");
        System.out.println();
        
        System.out.println("🔒 Security Notes:");
        System.out.println("   • Use the same credentials you use to log into Confluence");
        System.out.println("   • This handles the complete Atlassian ID authentication flow");
        System.out.println("   • Session cookies are managed automatically");
        System.out.println("   • Credentials are entered interactively (not stored)");
    }
    
    /**
     * Extract continue URL from redirect
     */
    private String extractContinueUrl(String redirectUrl) {
        try {
            String decoded = URLDecoder.decode(redirectUrl, "UTF-8");
            Pattern pattern = Pattern.compile("continue=([^&]+)");
            Matcher matcher = pattern.matcher(decoded);
            if (matcher.find()) {
                return URLDecoder.decode(matcher.group(1), "UTF-8");
            }
        } catch (Exception e) {
            // Ignore
        }
        return null;
    }
    
    /**
     * Extract meta redirect URL from HTML
     */
    private String extractMetaRedirect(String html) {
        Pattern pattern = Pattern.compile(
            "meta\\s+http-equiv=[\"']Refresh[\"']\\s+content=[\"'][^\"']*URL=([^\"']+)[\"']",
            Pattern.CASE_INSENSITIVE
        );
        
        Matcher matcher = pattern.matcher(html);
        if (matcher.find()) {
            return matcher.group(1);
        }
        
        return null;
    }
    
    /**
     * Save debug content
     */
    private void saveDebugContent(String content, String filename) {
        try {
            String sample = content.length() > 10000 ? content.substring(0, 10000) : content;
            try (FileWriter writer = new FileWriter(filename, StandardCharsets.UTF_8)) {
                writer.write("Debug content sample:\n");
                writer.write("=" + "=".repeat(50) + "\n\n");
                writer.write(sample);
                if (content.length() > 10000) {
                    writer.write("\n\n... (content truncated, total length: " + content.length() + " chars)");
                }
            }
            System.out.println("   💾 Saved debug content to: " + filename);
        } catch (Exception e) {
            System.out.println("   ⚠️  Debug save error: " + e.getMessage());
        }
    }
    
    /**
     * Create HTTP connection
     */
    private HttpURLConnection createConnection(String url) throws IOException {
        URL urlObj = new URL(url);
        HttpURLConnection conn = (HttpURLConnection) urlObj.openConnection();
        
        // Browser-like headers
        conn.setRequestProperty("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36");
        conn.setRequestProperty("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8");
        conn.setRequestProperty("Accept-Language", "en-US,en;q=0.5");
        conn.setRequestProperty("Accept-Encoding", "identity"); // Disable compression for easier debugging
        conn.setRequestProperty("Connection", "keep-alive");
        conn.setRequestProperty("Upgrade-Insecure-Requests", "1");
        
        conn.setConnectTimeout(15000);
        conn.setReadTimeout(30000);
        
        return conn;
    }
    
    /**
     * Read response
     */
    private String readResponse(HttpURLConnection conn) throws IOException {
        InputStream inputStream = conn.getInputStream();

        String encoding = conn.getContentEncoding();
        if ("gzip".equals(encoding)) {
            inputStream = new java.util.zip.GZIPInputStream(inputStream);
        } else if ("deflate".equals(encoding)) {
            inputStream = new java.util.zip.InflaterInputStream(inputStream);
        }

        try (BufferedReader reader = new BufferedReader(
                new InputStreamReader(inputStream, StandardCharsets.UTF_8))) {
            StringBuilder response = new StringBuilder();
            String line;
            while ((line = reader.readLine()) != null) {
                response.append(line).append("\n");
            }
            return response.toString();
        }
    }
    
    /**
     * Read error response
     */
    private String readErrorResponse(HttpURLConnection conn) {
        try (BufferedReader reader = new BufferedReader(
                new InputStreamReader(conn.getErrorStream(), StandardCharsets.UTF_8))) {
            StringBuilder response = new StringBuilder();
            String line;
            while ((line = reader.readLine()) != null) {
                response.append(line).append("\n");
            }
            return response.toString();
        } catch (Exception e) {
            return "Could not read error response: " + e.getMessage();
        }
    }
}
